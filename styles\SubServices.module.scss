@import "config";
@import "mixins";

.banner {
  overflow: hidden;
  padding-top: 147px;
  background: $milkWhite url("../public/images/sub-services-banner/bg.svg");
  margin-top: -122px;
  min-height: 800px;
  padding-bottom: 10px;


  @include media(sm) {
    padding-top: 104px;
    margin-top: -96px;
  }

  &__inner {
    @include container;
    display: flex;
    justify-content: space-between;
    @include media(md) {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  &__left {
    width: calc(50% - 110px);
    margin-top: 92px;

    @include media(lg) {
      width: calc(50% - 40px);
    }

    @include media(md) {
      width: 100%;
      margin-top: 0;
    }

  }

  &__right {
    width: 50%;
    max-width: 643px;
    height: 100%;

    @include media(md) {
      margin-top: 40px;
      width: 100%;
    }

    &__image {
      transform: translateX(30%);
      transition: .4s ease-in, .4s ease-in-out;
      opacity: 0;
    }

    &__image.animated {
      transform: translateX(0);
      opacity: 1;
    }
  }


  &__headline {
    margin-bottom: 28px;
    max-width: 538px;
    @include media(lg) {
      max-width: unset;
      margin-bottom: 16px;
    }
  }

  &__description {
    margin-bottom: 52px;
    font-size: 20px;
    line-height: 1.5;
    max-width: 508px;

    @include media(xs) {
      font-size: 16px;
      margin-bottom: 36px;
    }
  }

  &__buttons {
    display: flex;
    align-items: center;
    margin-bottom: 44px;
    @include media(sm) {
      margin-bottom: 28px;
    }

    &__image {
      max-width: 40px;
      height: 40px;
      width: 100%;

      circle {
        transition: .3s ease-in, .3s ease-in-out;
      }
    }

    &__start {
      margin-right: 24px;

      @include media(sm) {
        margin-right: 36px;
      }

      @include media(xs) {
        font-size: 16px;
      }
    }

    &__demo {
      display: flex;
      font-size: 20px;
      align-items: center;
      @include media(xs) {
        font-size: 16px;
      }

      &:hover {
        .banner__buttons__image {
          circle {
            fill: $greenBlue2;
          }
        }
      }

      span {
        margin-left: 16px;
        color: $black;
      }
    }
  }
}