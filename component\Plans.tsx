import Image from 'next/image'
import Link from 'next/link'
import styles from '../styles/Plans.module.scss'
import globalStyle from '../styles/Global.module.scss'
import availableIcon from '../public/images/icon/available_ic.svg'
import availableWhiteIcon from '../public/images/icon/available_white_ic.svg'
import {useState} from 'react'
import {PlanInterface} from "../interfaces/HomePageInterfaces";

type Props = {
  props: Array<PlanInterface>
}
const Plans = ({props}: Props) => {
  const [period, setPeriod] = useState('monthly')
  const [activeTab, setActiveTab] = useState(props[0].id)

  return (
    <section className={styles.plans}>
      <div className={styles.plans__inner}>
        <span className={`${styles.plans__tagline} ${globalStyle.tagline}`}>
          {props[0].tagline_landing_page}
        </span>
        <h2 className={styles.plans__headline}>
          {props[0].headline_landing_page}
        </h2>
        <form className={styles.plans__period}>
          <div className={styles.plans__period__item}>
            <input
              type="radio"
              name="period"
              id="monthly"
              className={styles.plans__period__checkbox}
              value="monthly"
              onChange={() => setPeriod('monthly')}
              checked={period === 'monthly'}
            />
            <label htmlFor="monthly" className={styles.plans__period__label}>Monthly</label>
          </div>
          <div className={styles.plans__period__item}>
            <input
              type="radio"
              className={styles.plans__period__checkbox}
              name="period"
              id="yearly"
              value="yearly"
              onChange={() => setPeriod('yearly')}
              checked={period === 'yearly'}
            />
            <label htmlFor="yearly" className={styles.plans__period__label}>Yearly</label>
          </div>
        </form>
        <div className={styles.plans__content}>
          <div className={styles.plans__packages}>
            {
              props && props.map((item, index) => {
                return (
                  <button key={index}
                       className={`${styles.plans__packages__item} ${activeTab === item.id ? styles.active : ''}`}
                       onClick={() => setActiveTab(item.id)}
                  >
                    {item.name}
                  </button>
                )
              })
            }
          </div>
          <div className={styles.plans__tabs}>
            {
              props && props.map((item, index) => {
                return item.id === activeTab ?
                  <div className={styles.plans__tabs__item} key={index}>
                    <div className={styles.plans__info}>
                      <div className={styles.plans__info__body}>
                  <span className={styles.plans__info__name}>
                  {item.Tier[0].name}
                </span>
                        <p className={styles.plans__info__price}>
                          {period === 'monthly' ? item.Tier[0].price_monthly : item.Tier[0].price_yearly}
                          <span>/month</span>
                        </p>
                        <p className={styles.plans__info__time}>
                        per month
                        </p>
                        <ul className={styles.plans__info__list}>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_1}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_2}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_3}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_4}
                          </li>
                        </ul>
                      </div>
                      <div className={styles.plans__info__bottom}>

                        <Link
                          href={'/registration'}
                        >
                          <a className={`${styles.plans__info__button} ${globalStyle.emptyButton}`}>
                            Get started
                          </a>
                        </Link>
                        <Link
                          href={'/pricing'}
                        >
                          <a className={styles.plans__info__more}>
                            {item.Tier[0].link_landing_page}
                          </a>
                        </Link>
                      </div>
                    </div>
                    <div className={`${styles.plans__info} ${styles.popular}`}>
                      <div className={styles.plans__info__body}>
                  <span className={styles.plans__info__name}>
                    {item.Tier[1].name}
                </span>
                        <p className={styles.plans__info__price}>
                          {period === 'monthly' ? item.Tier[1].price_monthly : item.Tier[1].price_yearly}
                          <span>/ month</span>
                        </p>
                        <p className={styles.plans__info__time}>
                        per month
                        </p>
                        <ul className={styles.plans__info__list}>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_1}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_2}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_3}
                          </li>
                        </ul>
                      </div>
                      <div className={styles.plans__info__bottom}>
                        <Link
                          href={'/registration'}
                        >
                          <a className={`${styles.plans__info__button} ${globalStyle.emptyButton}`}>
                            Get started
                          </a>
                        </Link>
                        <Link
                          href={'/pricing'}
                        >
                          <a className={styles.plans__info__more}>
                            {item.Tier[1].link_landing_page}
                          </a>
                        </Link>
                      </div>
                    </div>
                    <div className={styles.plans__info}>
                      <div className={styles.plans__info__body}>
                  <span className={styles.plans__info__name}>
                  {item.Tier[2].name}
                </span>
                        <p className={styles.plans__info__price}>
                          {period === 'monthly' ? item.Tier[2].price_monthly : item.Tier[2].price_yearly}
                          <span>/month</span>
                        </p>
                        <p className={styles.plans__info__time}>
                          
                        </p>
                        <ul className={styles.plans__info__list}>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_1}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_2}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_3}
                          </li>
                          <li className={styles.plans__info__item}>
                            <div className={styles.plans__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_4}
                          </li>
                        </ul>
                      </div>
                      <div className={styles.plans__info__bottom}>
                        <Link
                          href={'/registration'}
                        >
                          <a className={`${styles.plans__info__button} ${globalStyle.emptyButton}`}>
                            Get started
                          </a>
                        </Link>
                        <Link
                          href={'/pricing'}
                        >
                          <a className={styles.plans__info__more}>
                            {item.Tier[2].link_landing_page}
                          </a>
                        </Link>
                      </div>
                    </div>
                  </div>
                  : null
              })
            }
          </div>
        </div>
      </div>
    </section>
  )
}

export default Plans