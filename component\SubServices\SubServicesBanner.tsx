import { NextComponentType } from 'next'
import styles from '../../styles/SubServices.module.scss'
import globalStyle from '../../styles/Global.module.scss'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import { ServicesPageBannerInterface } from '../../interfaces/HomePageInterfaces'
import { useUser } from '@auth0/nextjs-auth0'

type Props = {
  props: ServicesPageBannerInterface
}

const SubServicesBanner = ({ props }: Props) => {
  const [animated, setAnimated] = useState(false)
  const { user } = useUser()

  useEffect(() => {
    setTimeout(() => {
      setAnimated(true)
    }, 400)

  }, [])

  return (
    <section className={styles.banner}>
      <div className={styles.banner__inner}>
        <div className={styles.banner__left}>
          <span className={`${styles.banner__tagline} ${globalStyle.tagline}`}>{props.tagline}</span>
          <h1 className={styles.banner__headline}>
            {props.headline}
          </h1>
          <p className={styles.banner__description}>
            {props.description}
          </p>
          <div className={styles.banner__buttons}>
            {
              user ?
                <a href="https://app.urecruits.com/" className={`${styles.banner__buttons__start} ${globalStyle.filledButton}`}>
                  {props.button_left}
                </a>
                :
                <Link
                  href={'/registration'}
                >
                  <a className={`${styles.banner__buttons__start} ${globalStyle.filledButton}`}>
                    {props.button_left}
                  </a>
                </Link>
            }
            <a href="#contactUs" className={styles.banner__buttons__demo}>
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                   className={styles.banner__buttons__image}>
                <circle cx="20" cy="20" r="20" fill="#029CA5"/>
                <path d="M15.5235 14L24.8568 20L15.5235 26V14Z" fill="white" stroke="white"
                      strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>{props.button_right}</span>
            </a>
          </div>
        </div>
        <div className={styles.banner__right}>
          <div className={`${styles.banner__right__image} ${animated ? styles.animated : ''}`}>
            <img src={props.image.url} alt="banner"/>
          </div>
        </div>
      </div>
    </section>
  )
}

export default SubServicesBanner