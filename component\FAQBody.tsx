import styles from '../styles/HelpCenter.module.scss'
import {FaqInterface, FaqSupportInterface} from "../interfaces/HomePageInterfaces";
import {useLayoutEffect, useState} from "react";
import FaqItem from "./FaqItem";
import FaqSupport from "./FaqSupport";
import {useRouter} from "next/router";


type Props = {
  props: FaqInterface,
  filter:string,
  support:FaqSupportInterface
}

const PricesBanner = ({props,filter,support}: Props) => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('Support')

  useLayoutEffect(() => {
    if (router.query.tab ==='faq'){
      setActiveTab('FAQ')
    }
  }, [])

  return (
    <section className={styles.faq}>
      <div className={styles.faq__inner}>
        <div className={styles.faq__left}>
          <div className={`${styles.faq__left__item} ${activeTab==='Articles'?styles.active:null}`}
          onClick={()=>setActiveTab('Articles')}
          >
            Articles
          </div>
          <div className={`${styles.faq__left__item} ${activeTab==='Tutorials'?styles.active:null}`}
               onClick={()=>setActiveTab('Tutorials')}
          >
            Tutorials
          </div>
          <div className={`${styles.faq__left__item} ${activeTab==='FAQ'?styles.active:null}`}
               onClick={()=>setActiveTab('FAQ')}
          >
            FAQ & Troubleshooting
          </div>
          <div className={`${styles.faq__left__item} ${activeTab==='Documentation'?styles.active:null}`}
               onClick={()=>setActiveTab('Documentation')}
          >
            Documentation
          </div>
          <div className={`${styles.faq__left__item} ${activeTab==='Support'?styles.active:null}`}
               onClick={()=>setActiveTab('Support')}
          >
            Support
          </div>
        </div>
        <div className={styles.faq__right}>
          {
            activeTab==='FAQ'&& <ul>
              {
                filter!==''?
                props.faqitem&&props.faqitem.length>0&&props.faqitem.filter((x)=>x.title.toLowerCase().includes(filter.toLowerCase())).map((item,key)=>{
                  return(
                    <FaqItem item={item} key={key}/>
                  )
                }):
                  props.faqitem&&props.faqitem.length>0&&props.faqitem.map((item,key)=>{
                    return(
                      <FaqItem item={item} key={key}/>
                    )
                  })
              }
            </ul>
          }
          {
            activeTab==='Support'&&<FaqSupport support={support}/>
          }
          {
            activeTab==='Articles'&&
              <>
                {/*<div className={styles.faq__info}>
                  <div className={styles.faq__info__image}></div>
                  <div className={styles.faq__info__right}>
                    <h3 className={styles.faq__info__title}>Beginner’s  guide</h3>
                    <p className={styles.faq__info__description}>Get the most out of your first days with Recruitee</p>
                    <div className={styles.faq__author}>
                      <div className={styles.faq__author__image}></div>
                      <div className={styles.faq__author__block}>
                        <p className={styles.faq__author__text}>9 articales on this collection</p>
                        <p className={styles.faq__author__text}> <span>Written by</span> Urecruits team</p>
                      </div>
                    </div>
                  </div>
                </div>*/}
              </>
          }


        </div>
      </div>
    </section>
  )
}

export default PricesBanner