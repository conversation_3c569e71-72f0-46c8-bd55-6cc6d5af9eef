@import "config";
@import "mixins";

.inner {
  @include container;
}
.passwordInner {
  position: relative;
  margin-bottom: 12px;

  input {
    padding-right: 32px;
  }

  .passwordType {
    width: 20px;
    height: 12px;
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(-9px, 17px);
    cursor: pointer;
  }
}
.form {
  width: 35%;
  @include media(sm){
    width: 100%;
  }
  &__input {
    @include input;

    &_last {
      margin-top: 12px;
    }

    &.error {
      border-color: $red;
    }
  }

  .errorMessage,
  {
    font-size: 12px;
    line-height: 1.2;
    color: $red;
    transform: translateY(4px);
  }

  &__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &__label {
    @include label;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 3;

    &:first-child {
      align-items: flex-start;
    }

    &:last-child {
      align-items: flex-end;
    }
  }

  &__submit {
    display: block;
    margin-left: auto;
    font-size: 14px;
  }

  &__buttons {
    margin-top: 12px;
  }
}