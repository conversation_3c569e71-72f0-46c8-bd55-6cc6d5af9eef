import {FilterEnums, JobEnums} from "../../types/redux-enums";
import {IFilter} from "../../types/jobs";

export const changeFilter = (payload: IFilter) => {
  return {type: FilterEnums.CHANGE_FILTER, payload}
}

export const changeLimit = (payload: number) => {
  return {
    type: FilterEnums.CHANGE_LIMIT, payload
  }
}

export const changeJobSubscribe = (payload: boolean) => {
  return {
    type: JobEnums.CHANGE_SUBSCRIBE_JOB, payload
  }
}

export const changeJobSave = (payload: boolean) => {
  return {
    type: JobEnums.CHANGE_SAVE_JOB, payload
  }
}

export const changeJobApply = (payload: boolean) => {
  return {
    type: JobEnums.CHANGE_APPLY_JOB, payload
  }
}