import {memo} from "react";
import Link from "next/link";
import styles from '../../styles/articles-theme.module.scss'
import React from "react";
import {IBreadCrumbs} from "../../interfaces/HomePageInterfaces";

export default memo(({array}: { array: Array<IBreadCrumbs> }) => {
	return <div className={styles.breadcrumb}>
		{
			array.map((item, index) => {
				return <React.Fragment key={index}>
					<Link href={item.link}>
						<a>{item.name}</a>
					</Link>
					{
						index!==array.length-1&&<p> / </p>
					}
				</React.Fragment>
			})
		}
	</div>
})