import styles from '../../styles/Search.module.scss'
import globalStyle from '../../styles/Global.module.scss'
import searchIcon from '../../public/images/icon/search_ic_white.svg'
import { NextComponentType } from 'next'
import { useState } from 'react'

type Props = {
  setSearchValue: (val: string) => void
  setSearchStatus: (val: boolean) => void
}

const Search = ({ setSearchValue, setSearchStatus }: Props) => {
  const [inputValue, setInputValue] = useState('')

  const onSubmitForm = (e: any) => {
    e.preventDefault()
    setSearchValue(inputValue)
    setSearchStatus(true)
  }

  return (
    <section className={styles.search}>
      <div className={styles.search__inner}>
        <p className={`${styles.search__tagline} ${globalStyle.tagline}`}>
          INtegrations
        </p>
        <h1 className={styles.search__headline}>
          Recruitee Marketplace
        </h1>
        <p className={styles.search__description}>
          Improve your hiring process with connected services
        </p>
        <form className={styles.search__form} onSubmit={(e: any) => onSubmitForm(e)}>
          <input
            type="text"
            className={styles.search__form__input} placeholder="Search for integrations ..."
            onChange={(e) => setInputValue(e.target.value)}
          />
          <button className={styles.search__form__submit}>
            <img src={searchIcon.src} alt="search icon" className={styles.search__form__icon}/>
          </button>
        </form>
      </div>
    </section>
  )
}

export default Search