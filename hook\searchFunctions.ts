import axios from "axios";

const config = {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  }
}

// TODO:need to add types for this func
export const selectSearchFuncLocation = async (searchValue: string) => {
  const req = await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/location/${searchValue}`, config).then((response) => response.json())

  return req.map((item: any) => {
    return {
      value: `${item.city}, ${item.state}`,
      label: `${item.city}, ${item.state}`,
      id: item.id
    }
  })
}

export const selectSearchFuncPosition = async (searchValue: any) => {
  return await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/position/${searchValue}`, config)
    .then((response) => response.json())
}

export const selectSearchFuncIndustry = async (searchValue: any) => {
  return await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/industry/label/${searchValue}`, config)
    .then((response) => response.json())
}

export const selectSearchFuncCompanies = async (searchValue: string) => {
  return await axios(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/company/for-job/`, {
    params: {
      search: searchValue
    }
  }).then((response) => response.data.rows.map((item:{name: string, tenantId: string, id: number}) => {
    return{
      label: item.name,
      value: item.tenantId,
      id: item.id
    }
  }))
}

export const selectSearchFuncSkills = async (searchValue: string) => {
  return await axios(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/skills/${searchValue}`)
    .then((response) => response.data.map((item:{name: string, tenantId: string, id: number}) => {
    return{
      label: item.name,
      value: item.name,
      id: item.id
    }
  }))
}