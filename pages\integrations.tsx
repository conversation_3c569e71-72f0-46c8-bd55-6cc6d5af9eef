import Head from 'next/head'
import Layout from '../component/Layout'
import Integrations from '../component/Integrations/Integrations'
import ContactUs from '../component/ContactUs'
import GetStarted from '../component/GetStarted'
import { ServicesPageInterface } from '../interfaces/HomePageInterfaces'
import { NextPage } from 'next'
import Search from '../component/Integrations/Search'
import { useState } from 'react'

interface Data {
  services: ServicesPageInterface
}

const IntegrationsPage: NextPage<Data> = ({ services }) => {
  const [searchValue, setSearchValue] = useState('')
  const [searchStatus, setSearchStatus] = useState(false)

  return (
    <Layout>
      <Head>
        <title>HR Software Integrations | Connect ATS, CRM & More</title>
        <meta name="description" content="Seamlessly integrate uRecruits with your ATS, CRM, payroll, and other tools. Streamline workflows and boost productivity with powerful HR tech connections." />
      </Head>
      <Search
        setSearchValue={setSearchValue}
        setSearchStatus={setSearchStatus}
      />
      <Integrations
        searchValue={searchValue}
        setSearchStatus={setSearchStatus}
        searchStatus={searchStatus}
      />
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

IntegrationsPage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const services = await response_services.json()
  return { services }
}

export default IntegrationsPage