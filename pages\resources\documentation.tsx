import SupportLayout from "../../component/SupportLayout";
import {HelpCenterInterface} from "../../interfaces/HomePageInterfaces";
import {NextPage} from "next";

interface Data {
	bannerData: HelpCenterInterface,
}

const Documentation: NextPage<Data> = ({bannerData}) => {

	return (
		<SupportLayout bannerData={bannerData.Banner}>
			<h1>Documentation</h1>
		</SupportLayout>
	);
};

export default Documentation;

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");

	return {
		props: {
			bannerData: await helpCenterPageRes.json(),
		},
	};
}

