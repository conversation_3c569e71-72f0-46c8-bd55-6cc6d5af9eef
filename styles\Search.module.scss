@import "config";
@import "mixins";

.search {
  padding-top: 182px;
  margin-top: -122px;
  background: $milkWhite url("../public/images/help-center/help-center-banner-bg.svg");

  &__inner {
    @include container();
    display: flex;
    flex-direction: column;
    align-items: center;
    @include media(xs){
      padding: 0 16px;
    }
  }

  &__headline {
    margin-bottom: 28px;
    @include media(xs) {
      margin-bottom: 16px;
    }
  }

  &__description {
    margin-bottom: 52px;
    font-family: 'Avenir LT Std', sans-serif;
    font-size: 20px;
    line-height: 1.5;
    color: $grayTone5;
    @include media(xs) {
      margin-bottom: 36px;
      font-size: 16px;
      text-align: center;
    }
  }

  &__form {
    max-width: 870px;
    width: 100%;
    position: relative;

    &__input {
      width: 100%;
      padding: 14px 80px 14px 24px;
      background: $white;
      border: 1px solid $grayTone2;
      font-size: 20px;
      @include media(xs) {
        font-size: 14px;
        padding: 11px 80px 11px 24px;
      }

      &::placeholder {
        color: $grayTone4;
      }
    }

    &__submit {
      transform: translate(-24px, 10px);
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      background: $greenGradient2;
      cursor: pointer;
      border-radius: 4px;
      top: 0;
      right: 0;
      opacity: .6;
      transition: .3s ease-in, .3s ease-in-out;

      &:hover {
        opacity: 1;
      }

      @include media(xs) {
        width: 24px;
        height: 24px;
        transform: translate(-16px, 10px);
      }
    }

    &__icon {
      width: 20px;
      min-width: 20px;
      height: 20px;
      object-fit: contain;

      @include media(xs) {
        width: 12px;
        min-width: 12px;
        height: 12px;
      }
    }
  }
}