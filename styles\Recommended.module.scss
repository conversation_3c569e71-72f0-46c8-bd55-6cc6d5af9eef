@import "mixins";
@import "config";

.recommended {
  width: 100%;
  padding: 40px;
  background: white;
  border: 1px solid $grayTone2;
  border-radius: 12px;
  @include media(sm){
    padding: 24px 16px;
  }

  &__headline {
    color: $grayTone6;
    font-size: 20px;
    font-family: "Poppins", sans-serif;
    line-height: 1;
    margin-bottom: 24px;
    font-weight: 600;
  }

  &__list {
    display: flex;
    flex-direction: column;
  }

  &__item {
    width: 100%;
    padding: 20px 0 12px 0;
    border-bottom: 1px solid $grayTone2;

    &:first-child {
      padding-top: 0;
    }
    &:last-child{
      padding-bottom: 0;
      border-bottom: 0;
    }
  }

  &__top {
    margin-bottom: 8px;
    @include media(sm){
      display: flex;
      justify-content: flex-end;
    }

    &__link {
      color: $greenBlue1;
      font-size: 14px;
      display: block;
    }
  }

  &__body {
    display: flex;
    align-items: center;
    @include media(sm){
      flex-direction: column;
    }

    &__image {
      width: 60px;
      height: 60px;
      margin-right: 12px;
      @include media(sm){
        margin-right: 0;
      }
    }

    &__inner {
      display: flex;
      flex-direction: column;
      @include media(sm){
        width: 100%;
      }
    }

    &__title {
      font-size: 16px;
      line-height: 1;
      color: $black;
      margin-bottom: 12px;
      @include media(sm){
        margin-top: 12px;
        text-align: center;
      }
    }
  }

  &__info {
    display: flex;
    @include media(sm){
      flex-direction: column;
      align-items: center;
    }

    &__item {
      display: flex;
      align-items: center;
      line-height: 1.4;
      font-size: 14px;
      color: $grayTone7;
      padding: 0 10px;
      position: relative;
      @include media(sm){
        margin-bottom: 12px;
      }

      &:after {
        content: "";
        position: absolute;
        width: 4px;
        height: 4px;
        background: $mainGreen;
        right: 0;
        top: calc(50% - 2px);
        transform: translateX(2px);
        border-radius: 50%;
        @include media(sm){
          display: none;
        }
      }

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        &:after {
          display: none;
        }
      }
    }

    &__download {
      width: 16px;
      height: 16px;
      min-width: 16px;
      margin-right: 8px;
    }
  }

  &__bottom {
    margin-top: 8px;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;

    &__item {
      font-size: 14px;
      line-height: 1;
      margin: 0 8px 8px 0;
      background: $grayTone2;
      color: $grayTone6;
      padding: 4px 8px;
      border-radius: 3px;
    }
  }
}