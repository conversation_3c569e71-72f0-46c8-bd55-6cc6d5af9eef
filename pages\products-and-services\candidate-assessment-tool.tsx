import Layout from '../../component/Layout'
import {NextPage} from 'next'
import Head from 'next/head'
import ContactUs from "../../component/ContactUs";
import GetStarted from "../../component/GetStarted";
import SubServicesBanner from "../../component/SubServices/SubServicesBanner";
import SubServicesList from "../../component/SubServices/SubServicesList";
import {ServicesPageInterface, ServicesPageTopInterfaces} from "../../interfaces/HomePageInterfaces";

interface Data {
  services: ServicesPageInterface
  services_pages: ServicesPageTopInterfaces
}

const AssessmentsPage: NextPage<Data> = ({services, services_pages}) => {
  return (
    <Layout>
      <Head>
        <title>Candidate Assessment Tool | Talent & Skill Assessment Software</title>
        <meta name="description" content="Discover uRecruits' candidate assessment tool and software. Run coding tests, domain evaluations, & behavioral assessments with online skill & talent assessment platforms for better hiring." />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org/",
          "@type": "SoftwareApplication",
          "name": "uRecruits Candidate Assessment Software",
          "applicationCategory": "HRSoftware",
          "applicationSubCategory": "PreEmploymentAssessmentTool",
          "operatingSystem": "Cloud, Windows, macOS",
          "downloadUrl": "https://urecruits.com/registration",
          "featureList": "https://urecruits.com/products-and-services/candidate-assessment-tool",
          "description": "uRecruits' Candidate Assessment Software helps employers evaluate applicants using customizable coding tests, skill assessments, and AI-powered scoring. Streamline screening and hire top talent faster with data-backed decision-making.",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "140",
            "bestRating": "5",
            "worstRating": "1"
          }
        })}} />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "How is uRecruits' assessment tool different?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Our platform is powered by Agentic AI, which dynamically creates and refines role-specific assessments based on industry, experience level, and past hiring outcomes. It's not just another test builder, it's a smart, self-improving system designed to deliver accuracy, speed, and fairness at scale."
              }
            },
            {
              "@type": "Question",
              "name": "What types of assessments can I conduct with uRecruits?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "With uRecruits, you can run: Live and take-home coding challenges, Behavioral and cognitive tests, Domain-specific scenarios, Writing samples, case studies, and logic puzzles, Whiteboard and video-based problem-solving sessions."
              }
            },
            {
              "@type": "Question",
              "name": "Do uRecruits provide software free trial?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Absolutely! We offer a free trial to help you test out our software and see if we are the right fit for your needs."
              }
            },
            {
              "@type": "Question",
              "name": "How much does uRecruits cost?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "We offer flexible plans. To get a quote, contact our sales team today."
              }
            },
            {
              "@type": "Question",
              "name": "How secure is uRecruits Candidate Assessment software?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Your and your candidate security is of paramount importance to us. That's why we implement encryption protocols, secure servers, and strict access controls to ensure security and data privacy."
              }
            },
            {
              "@type": "Question",
              "name": "How can uRecruits AI assessment tools optimize our hiring process?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "With our candidate assessment tool you can automate the evaluation process and eliminate manual biases, enabling you to pick the right talent faster with precision."
              }
            },
            {
              "@type": "Question",
              "name": "Which is the best candidate assessment tool in the US?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "While there are many tools on the market that can help you assess candidates, those can be too complicated to work with or do not integrate with your existing system. uRecruits AI assessment software for candidates unlocks all functionality for recruiters and managers."
              }
            },
            {
              "@type": "Question",
              "name": "Can I use uRecruits Candidate Assessment tool for non-technical assessments?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Absolutely! Our tool is designed to assess candidate skills for all roles and industries."
              }
            },
            {
              "@type": "Question",
              "name": "Is uRecruits Skill Assessment tool suitable for small businesses?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Yes. Our skill assessment tool is designed to cater to businesses of all sizes."
              }
            }
          ]
        })}} />
      </Head>
      <SubServicesBanner props={services_pages.Banner}/>
      <SubServicesList props={services_pages.row}/>
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

AssessmentsPage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const response_services_pages = await fetch('https://cms-dev.urecruits.com/assessments-page')
  const services = await response_services.json()
  const services_pages= await response_services_pages.json()
  return {services, services_pages}
}

export default AssessmentsPage