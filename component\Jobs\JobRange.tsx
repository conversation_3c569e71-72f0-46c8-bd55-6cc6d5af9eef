import styles from '../../styles/JobFilter.module.scss'
import { Range, getTrackBackground } from 'react-range'

interface RangeI {
  values: Array<number>
  setValues: (val: Array<number>) => void
  setPrevData: (val: Array<number>) => void
  maxValue: number
  minValue: number
}

const JobRange = ({values, setValues, maxValue, minValue, setPrevData}: RangeI) => {

    return(
    // @ts-ignore
    <Range
      values={values}
      step={1}
      min={minValue}
      max={maxValue}
      onChange={values => {
        setValues(values)
        setPrevData(values)
      }}
      renderTrack={({ props, children }: any) => (
        <div
          onMouseDown={props.onMouseDown}
          onTouchStart={props.onTouchStart}
          className={styles.range}
        >
          <div
            ref={props.ref}
            className={styles.range__scale}
            style={{
              background: getTrackBackground({
                values,
                colors: ['#ccc', '#099C73', '#ccc'],
                min: minValue,
                max: maxValue,
              }),
            }}
          >
            {children}
          </div>
        </div>
      )}
      renderThumb={({ props }: any) => (
        <div
          {...props}
          className={styles.range__thumb}
        >
          <div
            className={`${styles.range__thumb__icon} ${styles.left}`}
          />
          <div
            className={styles.range__thumb__icon}
          />
        </div>
      )}
    />
  )
}

export default JobRange