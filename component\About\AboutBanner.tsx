import styles from '../../styles/AboutBanner.module.scss'
import globalStyle from '../../styles/Global.module.scss'

const AboutBanner = ({ props }: any) => {

  return (
    <section className={styles.banner}>
      <div className={styles.banner__inner}>
        <div className={styles.banner__content}>
          <p className={`${styles.banner__tagline} ${globalStyle.tagline}`}>
          {props.tagline}
          </p>
          <h1 className={styles.banner__headline}>
            {props.headline}
          </h1>
          <p className={styles.banner__description}>
            {props.Description}
          </p>
        </div>
      </div>
    </section>
  )
}

export default AboutBanner