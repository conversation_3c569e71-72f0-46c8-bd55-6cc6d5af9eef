import { AppActionTypes, AppState } from '../../types/app'

const initialState: AppState = {
  visibleMobileMenu: false,
  applyPopup: {
    visible: false,
    jobId: 0,
    jobTitle: ''
  },
  successApplyPopup: false,
}

export const appReducer = (state = initialState, action: any):AppState => {
    switch (action.type){
      case AppActionTypes.MOBILE_NAVIGATION_MENU:
        return {...state, visibleMobileMenu: action.payload}
      case AppActionTypes.CHANGE_APPLY_POPUP:
        return {...state, applyPopup: action.payload}
      case AppActionTypes.CHANGE_SUCCESS_APPLY_POPUP:
        return {...state, successApplyPopup: action.payload}
      default:
        return state
    }
}