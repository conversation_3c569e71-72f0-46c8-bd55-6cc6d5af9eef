@import "config";
@import "mixins";

.banner{
  padding-bottom: 228px;
  padding-top: 216px;
  background: $milkWhite url("../public/images/services-banner/bg.svg") center;
  margin-top: -122px;
  background-size: cover;

  @include media(xs){
    padding-bottom: 200px;
    padding-top: 136px;
    margin-bottom: -96px;
  }

  &__inner{
    @include container;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    @include media(xs){
      align-items: start;
    }
    &:after{
      content: '';
      position: absolute;
      background: url("../public/images/icon/services-banner_ic.png") ;
      background-size: contain;
      width: 279px;
      height: 276px;
      bottom: 0;
      right: 0;
      transform: translate(2px, 188px);

    @include media(lg){
        width: 170px;
        height: 170px;
      }

      @include media(xs){
        width: 127px;
        height: 127px;
      }

      @include media(xss){
        transform: translate(-20px, 165px)
      }
    }
    &:before{
      height: 297px;
      width: 224px;
      background: url("../public/images/icon/arrow-services-banner_ic.svg");
      position: absolute;
      content: '';
      left: 0;
      bottom: 0;
      transform: translate(-20px, 351px);

      @include media(md){
        display: none;
      }
      @include media(xs){
        display: flex;
        width: 106px;
        height: 141px;
        background: url("../public/images/icon/arrow-services-small_ic.svg");
        transform: translate(17px, 250px);
      }
    }
  }


  &__headline{
    text-align: center;
    max-width: 896px;
    margin-bottom: 28px;
    @include media(xs){
      text-align: left;
      margin-bottom: 16px;
    }
  }

  &__description{
    max-width: 628px;
    margin-bottom: 52px;
    text-align: center;
    line-height: 1.5;

    @include media(xs){
      text-align: left;
      margin-bottom: 36px;
    }
  }

  &__buttons {
    display: flex;
    align-items: center;
    @include media(xs){

      justify-content: space-between;
    }


    &__image {
      max-width: 40px;
      height: 40px;
      width: 100%;

      circle{
        transition: .3s ease-in, .3s ease-in-out;
      }
    }

    &__start {
      margin-right: 24px;

      @include media(sm) {
        margin-right: 36px;
      }

      @include media(xs) {
        font-size: 16px;
      }
    }

    &__demo {
      display: flex;
      font-size: 20px;
      align-items: center;
      @include media(xs) {
        font-size: 16px;
      }

      &:hover{
        .banner__buttons__image{
          circle{
            fill: $greenBlue2;
          }
        }
      }

      span {
        margin-left: 16px;
        color: $black;
        @include media(xs){
          margin-left: 12px;
        }
      }
    }
  }

}