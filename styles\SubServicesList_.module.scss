@import "config";
@import "mixins";

.section {
padding: 70px 0 0;
  @include media(xs){
    padding: 50px 0 0;
  }
}

.inner {
  @include container;
}

.row {
  padding: 64px 0;
  width: 100%;

  &:nth-child(2n) {
      background: $milkWhite;
  }
  &:last-child{
    padding: 64px 0 70px;
  }
  @include media(xs) {
    padding: 32px 0;
    &:last-child{
      padding: 32px 0 50px;
    }
  }

}


.list {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% + 84px);
  margin: 0 -42px;
  @include media(xl){
    width: calc(100% + 32px);
    margin: 0 -16px;
  }
}

.item {
  width: calc(33.33% - 84px);
  margin: 52px 42px 0;

  @include media(xl) {
    margin: 36px 16px 0;
    width: calc(33.33% - 32px);
  }
  @include media(sm) {
    width: calc(50% - 32px);
  }
  @include media(xs) {
    width: calc(100% - 32px);
  }

  &__image {
    width: 100%;
    height: 180px;
    object-fit: contain;
    margin-bottom: 24px;
    @include media(xs) {
      margin-bottom: 36px;
    }
  }

  &__title {
    margin-bottom: 18px;
    font-weight: 900;
    color: $black;
    line-height: 1;
    @include media(xs) {
      margin-bottom: 16px;
      font-weight: 700;
      font-size: 20px;
      line-height: 1.6;

    }
  }

  &__desc {
    font-size: 16px;
  }
}
