import styles from "../../styles/AboutResult.module.scss";
import globalStyle from "../../styles/Global.module.scss";

import companyIc from "../../public/images/about/company_ic.svg";
import jobsIc from "../../public/images/about/jobs_ic.svg";
import teamIc from "../../public/images/about/team_ic.svg";

const AboutResult = ({ props }: any) => {
  return (
    <section className={styles.result}>
      <div className={styles.result__inner}>
        <div className={styles.result__left}>
          <p className={`${styles.result__tagline} ${globalStyle.tagline}`}>{props.tagline}</p>
          <h2 className={styles.result__headline}>{props.headline}</h2>
          <p className={styles.result__description}>{props.description}</p>
        </div>
        <div className={styles.result__right}>
          <div className={`${styles.result__info} ${styles.members}`}>
            <img src={teamIc.src} alt="icon" className={styles.result__info__icon} />
            <p className={styles.result__info__value}>{props.info[0].value}</p>
            <p className={styles.result__info__name}>{props.info[0].name}</p>
          </div>
          <div className={`${styles.result__info} ${styles.jobs}`}>
            <img src={jobsIc.src} alt="icon" className={styles.result__info__icon} />
            <p className={styles.result__info__value}>{props.info[1].value}</p>
            <p className={styles.result__info__name}>{props.info[1].name}</p>
          </div>
          <div className={`${styles.result__info} ${styles.clients}`}>
            <img src={companyIc.src} alt="icon" className={styles.result__info__icon} />
            <p className={styles.result__info__value}>{props.info[2].value}</p>
            <p className={styles.result__info__name}>{props.info[2].name}</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutResult;
