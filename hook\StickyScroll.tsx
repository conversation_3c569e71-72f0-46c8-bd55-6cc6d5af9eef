const stickyScroll = async (
  scrollRef: HTMLDivElement | null,
  distance: number,
  state: boolean
): Promise<boolean | null> => {
  let result: boolean | null = null
  if (window.scrollY > distance) {
    //checking if current position of scrollY more than position of block that needed to make sticky.
    result = true
  } else {
    if (window.scrollY < distance) {
      //removing styles if sticky block should have normal position in document
      result = false
    }
  }
  return result
}
export default stickyScroll