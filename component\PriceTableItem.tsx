import styles from '../styles/PriceTable.module.scss'
import {useState} from 'react'
import {NextComponentType} from "next";
import {CompareInterface} from "../interfaces/HomePageInterfaces";

type Props = {
  table: CompareInterface
}
const PriceTableItem = ({table}:Props) => {
  const [displayDetail, setDisplayDetail] = useState(true)

  return (
    <div className={`${styles.table__detail__item} ${displayDetail ? styles.hide : ''}`}>
      <div className={styles.table__detail__head} onClick={() => setDisplayDetail(!displayDetail)}>
        <p className={styles.table__detail__title}>
          {table.title}
        </p>
        <div className={styles.table__detail__arrow}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.75 15.75L12 7.5L2.25 15.75" stroke="#099C73" strokeWidth="1.77778" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      {
        displayDetail && (
          <ul className={styles.table__list}>
            {
              table.proses.map((tableItem, index) => {
                return (
                  <li className={styles.table__list__item} key={index}>
                    <div className={styles.table__list__cell}>
                      {tableItem.name}
                    </div>
                    <div className={styles.table__list__cell}>
                      {
                        tableItem.tier1_image?
                        <img src={tableItem.tier1_image.url} className={styles.table__list__image}/>
                        :tableItem.tier1_value
                      }
                    </div>
                    <div className={styles.table__list__cell}>
                      {
                        tableItem.tier2_image?
                          <img src={tableItem.tier2_image.url} className={styles.table__list__image}/>
                          :tableItem.tier2_value
                      }
                    </div>
                    <div className={styles.table__list__cell}>
                      {
                        tableItem.tier3_image?
                          <img src={tableItem.tier3_image.url} className={styles.table__list__image}/>
                          :tableItem.tier3_value
                      }
                    </div>
                  </li>
                )
              })
            }
          </ul>
        )
      }
    </div>
  )
}

export default PriceTableItem