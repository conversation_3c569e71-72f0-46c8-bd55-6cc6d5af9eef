/** @type {import('next').NextConfig} */
module.exports = {
  reactStrictMode: true,
  images: {
    loader: 'akamai',
    path: '/',
    domains: ['http://localhost:3000/'],
  },
  env: {
    GA_TRACKING_ID: 'G-1NKHFTFKCN',
    MAILCHIMP_AUDIENCE_ID: process.env.MAILCHIMP_AUDIENCE_ID,
    NEXT_PUBLIC_RECRUITMENT_API: process.env.NEXT_PUBLIC_RECRUITMENT_API
  },
  async redirects() {
    return [
      {
        source: '/products-and-services/recruitment',
        destination: '/products-and-services/recruitment-software',
        permanent: true,
      },
      {
        source: '/products-and-services/assessments',
        destination: '/products-and-services/candidate-assessment-tool',
        permanent: true,
      },
      {
        source: '/products-and-services/screening-and-hiring',
        destination: '/products-and-services/employee-background-screening-software',
        permanent: true,
      },
      {
        source: '/products-and-services/hr-analytics',
        destination: '/products-and-services/hr-analytics-software',
        permanent: true,
      },
    ]
  }
}