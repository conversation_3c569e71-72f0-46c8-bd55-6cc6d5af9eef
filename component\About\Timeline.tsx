import styles from '../../styles/Timeline.module.scss'
import { useState } from 'react'

const Timeline = ({ props }: any) => {
  const [step, setStep] = useState(1)

  return (
    
    <section className={styles.timeline}>
      <div className={styles.timeline__inner}>
        <div className={styles.timeline__content}>
          {props.map((item: any, index: number) => (
            <div
              key={item.id}
              className={`${styles.timeline__item} ${step === index + 1 ? styles.active : (step > index + 1 && styles.progress)}`}
            >
              <div className={styles.timeline__head}>
                {step === index + 1 && <p className={styles.timeline__head__value}>{item.Year}</p>}
              </div>
              <div className={styles.timeline__body}>
                <div className={styles.timeline__body__line}>
                  <div className={styles.timeline__body__dots} onClick={() => setStep(index + 1)}></div>
                </div>
              </div>
              <div className={styles.timeline__bottom}>
                {step === index + 1 && (
                  <>
                    <p className={styles.timeline__bottom__title}>{item.Title}</p>
                    <p className={styles.timeline__bottom__description}>{item.Description}</p>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
        <div className={styles.timeline__mobile}>
          <h2 className={styles.timeline__mobile__headline}>Company story</h2>
          {props.map((item: any) => (
            <div key={item.id} className={styles.timeline__mobile__item}>
              <p className={styles.timeline__mobile__year}>{item.Year}</p>
              <p className={styles.timeline__mobile__title}>{item.Title}</p>
              <p className={styles.timeline__mobile__description}>{item.Description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )  
}

export default Timeline