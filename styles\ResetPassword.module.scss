@import "config";
@import "mixins";

.reset {
  @include authSection;

  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include media(md) {
      justify-content: center;
    }
  }

  &__right {
    max-width: 815px;
    width: 100%;
    height: 680px;
    display: flex;
    align-items: center;
    @include media(md) {
      display: none;
    }
  }

  &__left {
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    padding-right: 20px;

    &__image{
      max-width: 60px;
      width: 100%;
      height: 60px;
      margin-bottom: 16px;
    }

    &__headline{
      font-weight: 500;
      margin-bottom: 40px;
    }

    &__description{
      font-size: 14px;
      color: $grayTone6;
    }

    &__link{
      color:$mainGreen;
    }
  }

  &__headline {
    font-weight: 500;
    margin-bottom: 37px;
    @include media(xs){
      margin-bottom: 33px;
    }
  }

  &__form.error {
    .errorMessage {
      display: block;
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    position: relative;

    .errorMessage {
      display: none;
      font-size: 12px;
      line-height: 1;
      color: $red;
      position: absolute;
      top: 100%;
      left: 0;
      width: 100%;
      transform: translateY(-65px);
    }

    &__text{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__label{
      @include label;
    }

    &__help{
      font-weight: 350;
      font-size: 12px;
      line-height: 1;
      color: $grayTone4;
    }

    &__input{
      @include input;
      margin-bottom: 12px;
    }

    &__buttonWrap{
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      @include media(xs){
        margin-top: 24px;
      }
    }

    &__button{
      font-weight: 800;
      font-size: 14px;
      line-height: 100%;
      color: $grayTone1;
    }
  }
}