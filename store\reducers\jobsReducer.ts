import {FilterEnums, JobEnums} from "../../types/redux-enums";
import {IJobs} from "../../types/jobs";

export const jobsReducer = (state: IJobs = initialState, action: any) => {
  switch (action.type) {
    case FilterEnums.CHANGE_FILTER:
      return {...{}, ...state, filters: action.payload}
    case FilterEnums.CHANGE_LIMIT:
      return {...{}, ...state, limit: action.payload}
    case JobEnums.CHANGE_SAVE_JOB:
      return {...{}, ...state, saveJob: action.payload}
    case JobEnums.CHANGE_SUBSCRIBE_JOB:
      return {...{}, ...state, subscribeJob: action.payload}
    case JobEnums.CHANGE_APPLY_JOB:
      return {...{}, ...state, applyJob: action.payload}
    default:
      return state
  }
}

export const initialState: IJobs = {
  limit: 10,
  filters: {
    jobTitle: '',
    jobLocation: null,
    postedOnData: null,
    salaryMonthValue: [1, 20000],
    salaryYearValue: [1, 1000000],
    experienceYearValue: [1, 50],
    education: null,
    skills: null,
    companyName: null,
    jobType: null,
    preferableShift: null,
    industryType: null,
    functionalArea: null,
  },
  saveJob: false,
  subscribeJob: false,
  applyJob: false
}