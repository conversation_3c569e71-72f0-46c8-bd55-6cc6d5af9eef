import SupportLayout from "../../../component/SupportLayout";
import { HelpCenterInterface } from "../../../interfaces/HomePageInterfaces";
import { NextPage } from "next";
import GivingBackToCommunitiesPost from "../../../component/GivingBackToCommunities/GivingBackToCommunitiesPost";


interface Data {
  bannerData: HelpCenterInterface;
  givingBackToCommunitiesData: any;
}


const GivingBackToCommunitiesDetail: NextPage<Data> = ({ bannerData,givingBackToCommunitiesData }) => {
  return (
    <SupportLayout bannerData={bannerData.Banner}>
      
      <GivingBackToCommunitiesPost props={givingBackToCommunitiesData} />
     
    </SupportLayout>
  );
};

export default GivingBackToCommunitiesDetail;

export async function getServerSideProps(context: { res: any; req: any; query: any }) {
  const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
  const givingBackToCommunitiesPageRes = await fetch(`https://cms-dev.urecruits.com/giving-back-to-communities-lists?post=${context.query.post}`);
  return {
    props: {
      bannerData: await helpCenterPageRes.json(),
      givingBackToCommunitiesData: await givingBackToCommunitiesPageRes.json(),
    },
  };
}