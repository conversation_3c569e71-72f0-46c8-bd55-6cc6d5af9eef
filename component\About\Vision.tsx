import styles from '../../styles/Vision.module.scss'
import Image from 'next/image'
import visionIc from '../../public/images/vision/vision_bg.svg'
import missionIc from '../../public/images/vision/mission_bg.svg'


const Vision  = ({ props }: any) => {

  return (
    <section className={styles.vision}>
      <div className={styles.vision__inner}>
        <div className={styles.vision__item}>
          <div className={styles.vision__item__image}>
            <Image src={visionIc}/>
          </div>
          <p className={styles.vision__item__tagline}>
            {props.vision_item[0].tagline}
          </p>
          <p className={styles.vision__item__title}>
          {props.vision_item[0].title}
          </p>
        </div>
        <div className={styles.vision__item}>
          <div className={styles.vision__item__image}>
            <Image src={missionIc}/>
          </div>
          <p className={styles.vision__item__tagline}>
          {props.vision_item[1].tagline}
          </p>
          <p className={styles.vision__item__title}>
          {props.vision_item[1].title}
          </p>
        </div>
      </div>
    </section>
  )
}

export default Vision