import Layout from '../../component/Layout'
import {NextPage} from 'next'
import Head from 'next/head'
import ContactUs from "../../component/ContactUs";
import GetStarted from "../../component/GetStarted";
import SubServicesBanner from "../../component/SubServices/SubServicesBanner";
import SubServicesList from "../../component/SubServices/SubServicesList";
import {ServicesPageInterface, ServicesPageTopInterfaces} from "../../interfaces/HomePageInterfaces";

interface Data {
  services: ServicesPageInterface
  services_pages: ServicesPageTopInterfaces
}

const HRAnalyticsPage: NextPage<Data> = ({services, services_pages}) => {
  return (
    <Layout>
      <Head>
        <title>HR Analytics Software | HR Management & Analytics Tool</title>
        <meta name="description" content="uRecruits offers advanced HR analytics tools to improve HR management. Discover powerful HR management analytics software for smarter, data-driven decisions." />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org/",
          "@type": "SoftwareApplication",
          "name": "uRecruits HR Analytics Software",
          "applicationCategory": "HRSoftware",
          "applicationSubCategory": "PeopleAnalyticsSoftware",
          "operatingSystem": "Cloud, Windows, macOS",
          "downloadUrl": "https://urecruits.com/registration",
          "featureList": "https://urecruits.com/products-and-services/hr-analytics-software",
          "description": "uRecruits HR Analytics Software delivers actionable insights across the employee lifecycle with visual dashboards, performance tracking, diversity reporting, and AI-powered hiring funnel optimization.",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "120",
            "bestRating": "5",
            "worstRating": "1"
          }
        })}} />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "What is HR Analytics?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "HR Analytics—also called People Analytics—is about using employee and hiring data to make smarter HR decisions. It helps you spot trends, solve problems, and create a better workplace by turning data into clear, useful insights."
              }
            },
            {
              "@type": "Question",
              "name": "What Are the 4 Types of HR Analytics?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Descriptive – What's happening right now? Diagnostic – Why is it happening? Predictive – What's likely to happen next? Prescriptive – What should we do about it? Each type helps HR teams go from just knowing the numbers to making strategic moves."
              }
            },
            {
              "@type": "Question",
              "name": "What Is a KPI in HR Analytics?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "KPIs (Key Performance Indicators) are the numbers that matter most—like time to hire, employee turnover, or engagement scores. They show how well your HR strategies are working and where to focus next."
              }
            },
            {
              "@type": "Question",
              "name": "Is HR Analytics a Skill?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Yes—and a powerful one. Top HR analysts are great at reading data, building reports, spotting patterns, and using tools like Excel, HR tech, or even AI. It's a mix of people understanding and number-crunching."
              }
            }
          ]
        })}} />
      </Head>
      <SubServicesBanner props={services_pages.Banner}/>
      <SubServicesList props={services_pages.row}/>
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

HRAnalyticsPage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const response_services_pages = await fetch('https://cms-dev.urecruits.com/hr-analytics-page')
  const services = await response_services.json()
  const services_pages= await response_services_pages.json()
  return {services, services_pages}
}

export default HRAnalyticsPage