@import "config";
@import "mixins";

.checkEmail {
  @include authSection;


  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include media(md) {
      justify-content: center;
    }
  }

  &__headline {
    margin-bottom: 37px;
    font-weight: 500;
    font-size: 28px;
    @include media(xs) {
      margin-bottom: 33px;
      font-size: 24px;
      line-height: 1.5;
    }
  }

  &__right {
    max-width: 815px;
    width: 100%;
    height: 680px;
    display: flex;
    align-items: center;
    @include media(md) {
      display: none;
    }
  }


  &__info {
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    padding-right: 20px;

    &__dynamic {
      display: none;
      margin-top: 16px;
      color: $blue;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.4;
      padding: 16px 28px;
      background: #EEFAFF;
      border: 1px solid $blue;
      box-sizing: border-box;
      text-align: center;
      border-radius: 8px;
      max-width: 335px;

      @include media(md) {
        max-width: 100%;
      }
    }

    &__dynamicActive {
      display: block;
    }

    &__headline {
      font-weight: 500;
      color: $black;
      line-height: 1.4;
      font-family: 'Poppins', sans-serif;
      font-size: 28px;
      max-width: 364px;

    }

    &__description {
      font-size: 14px;
      line-height: 1.4;
      color: $grayTone6;
      margin-bottom: 15px;
    }

    &__descriptionFirst {
      margin-top: 40px;
    }

    &__descriptionGreen {
      color: $mainGreen;
    }

    &__descriptionBold {
      font-weight: 800;
    }

    &__buttonWrap {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 56px;
      @include media(xs) {
        margin-top: 21px;
      }
    }

    &__button {
      font-size: 14px;
    }

    &__image {
      max-width: 100%;
      height: 60px;
      width: 60px;
      margin-bottom: 16px;

      @include media(xs) {
        margin-bottom: 18px;
      }
    }
  }
}


