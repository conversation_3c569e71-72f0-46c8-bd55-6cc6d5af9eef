import {NextPage} from 'next'
import globalStyles from "../styles/Global.module.scss";
import styles from '../styles/CheckEmail.module.scss'
import AuthLayout from "../component/Auth/AuthLayout";
import Image from "next/image";
import chat from "../public/images/icon/chat_ic.svg";
import email_ic from "../public/images/icon/forgot-password-right-email_ic.svg";
import {useState} from "react";


const CheckYourEmail: NextPage = () => {
  const [resentInfo, setResentInfo] = useState(false)
  const email = '<EMAIL>'
  const sendHandler = (): void => {
    setResentInfo(true)
  }
  return (
    <AuthLayout>
      <section className={styles.checkEmail}>
        <div className={styles.checkEmail__inner}>
          <div className={styles.checkEmail__info}>
            <div className={styles.checkEmail__info__image}>
              <Image src={chat} alt='chat'/>
            </div>
            <p className={styles.checkEmail__info__headline}>
              Please check your email for a verification link
            </p>
            <p
              className={`${styles.checkEmail__info__dynamic} ${resentInfo ? styles.checkEmail__info__dynamicActive : null}`}>
              Email resent. Please check your email again.
            </p>
            <p className={`${styles.checkEmail__info__description} ${styles.checkEmail__info__descriptionFirst}`}>
              We've sent a confirmation email to <span
              className={styles.checkEmail__info__descriptionGreen}> {email}</span> Your account needs to be verified
              before you can access it.
            </p>
            <p className={styles.checkEmail__info__description}>
              Your verification link will expire <span
              className={styles.checkEmail__info__descriptionBold}>in 48 hours</span> if you still haven't received a
              verification email please click the button below to resend it.
            </p>
            <div className={styles.checkEmail__info__buttonWrap}>
              <button
                onClick={sendHandler}
                className={`${globalStyles.filledButton} ${styles.checkEmail__info__button}`}>
                Resend email
              </button>
            </div>
          </div>
          <div className={styles.checkEmail__right}>
            <Image src={email_ic} alt='forgot-password'/>
          </div>
        </div>
      </section>
    </AuthLayout>
  )
}

export default CheckYourEmail