import SupportLayout from "../../component/SupportLayout";
import {FaqInterface, HelpCenterInterface} from "../../interfaces/HomePageInterfaces";
import {NextPage} from "next";
import FaqItem from "../../component/FaqItem";

interface Data {
	bannerData: HelpCenterInterface,
	faq: FaqInterface
}

const Faq: NextPage<Data> = ({bannerData, faq}) => {
	//TODO: need to add filter logic, fix ts error
	const filter = "";

	return (
		<SupportLayout bannerData={bannerData.Banner}>
			<ul>
				{
					filter !== "" ?
						// @ts-ignore
						faq.faqitem && faq.faqitem.length > 0 && faq.faqitem.filter((x) => x.title.toLowerCase().includes(filter.toLowerCase())).map((item, key) => {
							return (
								<FaqItem item={item} key={key}/>
							);
						}) :
						faq.faqitem && faq.faqitem.length > 0 && faq.faqitem.map((item, key) => {
							return (
								<FaqItem item={item} key={key}/>
							);
						})
				}
			</ul>
		</SupportLayout>
	);
};

export default Faq;

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
	const faqRes = await fetch("https://cms-dev.urecruits.com/faq");

	return {
		props: {
			bannerData: await helpCenterPageRes.json(),
			faq: await faqRes.json(),
		},
	};
}
