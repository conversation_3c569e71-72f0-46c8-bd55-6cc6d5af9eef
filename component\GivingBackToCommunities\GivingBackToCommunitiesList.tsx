import Link from "next/link";

const GivingBackToCommunitiesList = ({ props }: any) => {
  return (
    <Link href={`/giving-back-to-communities/${props.post}`} passHref>
    <div className="p-4 cursor-pointer">
        <div className="flex flex-row justify-start text-gray-500 border border-solid border-zinc-200 rounded-lg overflow-hidden gap-x-10">
          <img className="cursor-pointer object-fill h-36 w-52 object-left" src={props.image.url} />
          <div className="flex flex-col gap-y-6">
            <h2 className="text-zinc-800 text-2xl font-bold pt-6 tracking-wide">{props.Title}</h2>
            <p className="font-['Avenir LT Std'] text-zinc-500  tracking-wide text-lg font-normal">{props.Summary}</p>
          </div>
        </div>
    </div>
    </Link>
  );
};

export default GivingBackToCommunitiesList;
