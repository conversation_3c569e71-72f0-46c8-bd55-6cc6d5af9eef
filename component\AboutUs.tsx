import styles from '../styles/AboutUs.module.scss'
import globalStyle from '../styles/Global.module.scss'
import { AboutUsInterface, AboutUsItem } from "../interfaces/HomePageInterfaces";

type Props = {
  props: AboutUsInterface
}
const AboutUs = ({props}: Props) => {

  const renderItems = (items: AboutUsItem[]) => {
    return items.map((item, index) => {
      return (
        <div className={styles.about__information__item} key={index}>
          <p className={styles.about__information__value}>
            {item.value}
          </p>
          <p className={styles.about__information__name}>
            {item.text}
          </p>
        </div>
      )
    })
  }

  return (
    <section className={styles.about} id='about-us'>
      <div className={styles.about__inner}>
        <div className={styles.about__left}>
          <img src={props.image.url} alt="about image"/>
        </div>
        <div className={styles.about__right}>
          <span className={`${styles.about__tagline} ${globalStyle.tagline}`}>
            {props.tagline}
          </span>
          <h2 className={styles.about__headline}>
            {props.headline}
          </h2>
            <p className={styles.about__text}>
              {props.description}
            </p>
            <div className={styles.about__information}>
              <div className={styles.about__information__inner}>
                {renderItems(props.aboutItem)}
              </div>
            </div>
        </div>
      </div>
    </section>
  )
}

export default AboutUs