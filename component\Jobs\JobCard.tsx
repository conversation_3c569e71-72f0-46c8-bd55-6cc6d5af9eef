import styles from "../../styles/JobCard.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import { JobCardInterface } from "../../interfaces/HomePageInterfaces";
import companyImage from "../../public/images/default-company.svg";
import Link from "next/link";
import { numberWithCommas } from "../../hook/numberWithCommas";
import axios from "axios";
import { useState } from "react";
import { store } from "../../store";
import { changeApplyPopup, changeSuccessApplyPopup } from "../../store/action-creator/app";
import { useRouter } from "next/router";

const JobCard = ({ data, displayFormat, permissions, token, companyId }: JobCardInterface) => {
	const router = useRouter();
	const [saveJobStatus, setSaveJobStatus] = useState(data.subscribes ? !!data.subscribes[0]?.saveJob : false);
	const [applyJobStatus, setApplyJobStatus] = useState(data.subscribes ? !!data.subscribes[0]?.applyJob : false);

	const onSaveHandler = (jobId: number, status: boolean) => {
		if (token !== "Empty") {
			axios.post(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/subscribe`, {
				saveJob: status,
				jobId: jobId,
			}, {
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${token}`,
				},
			}).then(() => setSaveJobStatus(!saveJobStatus));
		} else {
			const expirationDate = new Date();
			expirationDate.setTime(expirationDate.getTime() + 300000); // 5 minute in milliseconds
			document.cookie = `jobId=${jobId}; expires=${expirationDate.toUTCString()}; path=/; domain=.urecruits.com`;
			router.push("/api/auth/login");
		}
	};

	const onApplyHandler = (jobId: number) => {
		if (token !== "Empty") {
			axios.post(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/subscribe`, {
				applyJob: true,
				jobId: jobId,
			}, {
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${token}`,
				},
			}).then((res) => {
				if (res.status === 201) {
					store.dispatch(changeSuccessApplyPopup(true));
					setApplyJobStatus(!applyJobStatus)
				}
			});
		} else {

			const expirationDate = new Date();
			expirationDate.setTime(expirationDate.getTime() + 300000); // 5 minute in milliseconds
			document.cookie = `jobId=${jobId}; expires=${expirationDate.toUTCString()}; path=/; domain=.urecruits.com`;
			const redirectUri = encodeURIComponent(`/job/${jobId}/apply`)
			router.push(`/api/auth/login?redirectUri=${redirectUri}`);
		}
	};

	return (
		<div className={`${styles.card} ${displayFormat === "list" ? styles.listStyle : ""}`}>
			<div className={styles.card__head}>
				<div className={styles.card__head__left}>
					<div className={styles.card__head__logo}>
						<img src={data?.company?.avatar ? data?.company?.avatar : companyImage?.src} alt={data?.title} />
					</div>
					<div className={styles.card__head__info}>
						<Link href={`/job/${data?.id}`}>
							<a className={styles.card__head__name}>
								{data?.title}
							</a>
						</Link>
						<div className={styles.card__company}>
							{
								data?.match_percentage ? <div className={styles.card__company__name}>
								{data?.match_percentage} %
							</div> : ''
							}
							<div className={data?.match_percentage ? styles.card__company__location : styles.card__company__name}>
								{data?.company?.name}
							</div>
							<div className={styles.card__company__location}>
								<span>{data?.locations[0]?.city}, {data?.locations[0]?.state}</span>
							</div>
							{
								data.remoteLocation && (
									<div className={styles.card__company__remote}>
										Remote
									</div>
								)
							}
						</div>
					</div>
				</div>
				{
					displayFormat === "list" && (
						<div className={styles.card__head__right}>
							{
								!permissions ?
									<>
										<div
											className={` ${styles.card__control} ${saveJobStatus ? styles.active : ""}`}
											onClick={() => onSaveHandler(data?.id, !saveJobStatus)}
										>
											<div className={styles.card__control__icon}>
												<SaveIcon />
											</div>
											<p className={styles.card__control__text}>Save</p>
										</div>
										{
											data.applicationForm[0] ? (
												<div
													className={`${styles.card__control}  ${!applyJobStatus ? "" : styles.disabled}`}
													onClick={() => store.dispatch(changeApplyPopup({
														visible: true,
														jobId: data?.id,
														jobTitle: data?.title,
													}))}
												>
													<div className={styles.card__control__icon}>
														<ApplyIcon />
													</div>
													<span className={styles.card__control__text}>{!applyJobStatus ? "Apply" : "Applied"}</span>
												</div>
											)
												: (
													<div
														className={`${styles.card__control}  ${!applyJobStatus ? "" : styles.disabled}`}
														onClick={() => onApplyHandler(data.id)}
													>
														<div className={styles.card__control__icon}>
															<ApplyIcon />
														</div>
														<span className={styles.card__control__text}>{!applyJobStatus ? "Apply" : "Applied"}</span>
													</div>
												)
										}
									</>
									:
									<>
										{companyId && data?.company?.id && (companyId === data.company?.id) ? <Link href={`https://app.urecruits.com/job/${data.id}/edit`}>
											<div className={styles.card__control}>
												<a className={styles.card__control__text}>
													Edit
												</a>
											</div>
										</Link>
											: ''
										}
									</>
							}
						</div>
					)
				}
			</div>
			<div className={styles.card__body}>
				<ul className={styles.card__tags}>
					{
						data?.skills?.map((item: any, index: number) => {
							return (
								<li className={styles.card__tags__item} key={index}>
									{item}
								</li>
							);
						})
					}
				</ul>
				<div className={styles.card__description}>
					{data?.shortDescription}
				</div>
				<div className={styles.card__information}>
					<p className={styles.card__information__exp}>
						{data?.experienceMin}-{data?.experienceMax} years
					</p>
					<p className={styles.card__information__salary}>
						${numberWithCommas(data?.salaryYearMin)}K - ${numberWithCommas(data?.salaryYearMax)}K PA
					</p>
				</div>
			</div>
			<div className={styles.card__bottom}>
				{
					!permissions ?
						<>
							<div
								className={`${styles.card__button} ${styles.save} ${globalStyle.emptyButton} ${saveJobStatus ? styles.active : ""}`}
								onClick={() => onSaveHandler(data?.id, !saveJobStatus)}
							>
								<div className={styles.card__button__icon}>
									<SaveIcon />
								</div>
								<span className={styles.card__button__text}>{!saveJobStatus ? "Save" : "Unsave"}</span>
							</div>
							{
								data?.applicationForm[0] ? (
									<div
										className={`${styles.card__button} ${globalStyle.filledButton} ${!applyJobStatus ? "" : styles.disabled}`}
										onClick={() => store.dispatch(changeApplyPopup({
											visible: true,
											jobId: data?.id,
											jobTitle: data?.title,
										}))}
									>
										<div className={styles.card__button__icon}>
											<ApplyIcon />
										</div>
										<span className={styles.card__button__text}>{!applyJobStatus ? "Apply" : "Applied"}</span>
									</div>
								)
									: (
										<div
											className={`${styles.card__button} ${globalStyle.filledButton} ${!applyJobStatus ? "" : styles.disabled}`}
											onClick={() => onApplyHandler(data?.id)}
										>
											<div className={styles.card__button__icon}>
												<ApplyIcon />
											</div>
											<span className={styles.card__button__text}>{!applyJobStatus ? "Apply" : "Applied"}</span>
										</div>
									)
							}
						</>
						:
						<>
							{
								companyId === data.company.id ? <Link href={`https://app.urecruits.com/job/${data?.id}/edit`}>
									<a className={`${styles.card__button} ${globalStyle.filledButton}`}>
										Edit
									</a>
								</Link> : ''
							}
						</>
				}
			</div>
		</div>
	);
};

export default JobCard;

const SaveIcon = () => {
	return (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M20 22L12 16.4444L4 22V4.22222C4 3.63285 4.24082 3.06762 4.66947 2.65087C5.09812 2.23413 5.67951 2 6.28571 2H17.7143C18.3205 2 18.9019 2.23413 19.3305 2.65087C19.7592 3.06762 20 3.63285 20 4.22222V22Z"
				stroke="#099C73" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
		</svg>
	);
};

const ApplyIcon = () => {
	return (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M22 13.2001V9.00146C22 7.8969 21.1046 7.00146 20 7.00146H4C2.89543 7.00146 2 7.8969 2 9.00146V19.0015C2 20.106 2.89543 21.0015 4 21.0015H12"
				stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path
				d="M16 10.2V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21"
				stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<circle cx="16.8016" cy="16.8001" r="6.45" stroke="white" strokeWidth="1.5" />
			<path d="M16.8008 14.3999V19.1999" stroke="white" strokeWidth="1.5" strokeLinecap="round"
				strokeLinejoin="round" />
			<path d="M19.1984 16.8H14.3984" stroke="white" strokeWidth="1.5" strokeLinecap="round"
				strokeLinejoin="round" />
		</svg>
	);
};