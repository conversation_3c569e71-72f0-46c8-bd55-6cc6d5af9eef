import styles from "../styles/GetStarted.module.scss";
import Link from "next/link";
import globalStyle from "../styles/Global.module.scss";
import {PricingInterface} from "../interfaces/HomePageInterfaces";
import {useUser} from "@auth0/nextjs-auth0";

type Props = {
	props: PricingInterface
}

const GetStarted = ({props}: Props) => {
	// @ts-ignore
	// @ts-ignore
	return (
		<section className={styles.getStarted}>
			<div className={styles.getStarted__inner}>
				<span className={`${styles.getStarted__tagline} ${globalStyle.tagline}`}>{props.custom_plan.tagline}</span>
				<h2 className={styles.getStarted__headline}>
					{props.custom_plan.headline_first}<span>{props.custom_plan.headline_middle}</span>{props.custom_plan.headline_last}
				</h2>
				<h5 className={styles.getStarted__description}>
					{props.custom_plan.description}
				</h5>
				<div className={styles.getStarted__block}>
					<div className={styles.getStarted__block__left}>
						<Link href="https://resources.urecruits.com">
							<a
								className={`${styles.getStarted__block__buttonFree} ${globalStyle.filledButton}`}>{props.custom_plan.button_left}</a>
						</Link>
						<p className={styles.getStarted__block__description}>{props.custom_plan.text_left}</p>
					</div>
					<div className={styles.getStarted__block__right}>
						<a href="#contactUs"
						   className={`${styles.getStarted__block__buttonDemo} ${globalStyle.emptyButton}`}>{props.custom_plan.button_right}</a>
						<p className={styles.getStarted__block__description}>{props.custom_plan.text_right}</p>
					</div>
				</div>
			</div>
		</section>
	);
};

export default GetStarted;