import styles from '../styles/Registration.module.scss'
import globalStyles from '../styles/Global.module.scss'
import AuthLayout from '../component/Auth/AuthLayout'
import Image from 'next/image'
import Link from 'next/link'

import candidatePicture from '../public/images/registration/role/candidate_picture.svg'
import companyPicture from '../public/images/registration/role/company_picture.svg'
import bg from '../public/images/registration/role/bg.svg'
import Head from "next/head";

const Registration = () => {
  return (
    <AuthLayout>
      <Head>
        <title>Registration | uRecruits</title>
      </Head>
      <section className={styles.registration}>
        <div className={styles.registration__inner}>
          <div className={styles.registration__left}>
            <span className={globalStyles.tagline}>Welcome to uRecruits</span>
            <h2 className={styles.registration__headline}>Choose your role</h2>
            <div className={styles.registration__role}>
              <Link
                href={'/registration/company'}
              >
                <a className={styles.registration__role__image}>
                  <Image
                    src={companyPicture}
                  />
                </a>
              </Link>
              <Link
                href={'/registration/company'}
              >
                <a className={styles.registration__role__link}>
                  <span className={styles.registration__role__name}>
                    Company
                  </span>
                  <svg width="24" height="24" viewBox="0 0 24 24">
                    <path
                      d="M2.75 12H21.25M21.25 12L12.75 3.5M21.25 12L12.75 20.5"
                      stroke="#099C73"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </a>
              </Link>
            </div>
            <div className={styles.registration__role}>
              <Link
                href={'/registration/candidate'}
              >
                <a className={styles.registration__role__image}>
                  <Image
                    src={candidatePicture}
                  />
                </a>
              </Link>
              <Link
                href={'/registration/candidate'}
              >
                <a className={styles.registration__role__link}>
                  <span className={styles.registration__role__name}>
                    Candidate
                  </span>
                  <svg width="24" height="24" viewBox="0 0 24 24">
                    <path
                      d="M2.75 12H21.25M21.25 12L12.75 3.5M21.25 12L12.75 20.5"
                      stroke="#099C73"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </a>
              </Link>
            </div>
          </div>
          <div className={styles.registration__right}>
            <div className={styles.registration__right__image}>
              <Image src={bg}/>
            </div>
          </div>
        </div>
      </section>
    </AuthLayout>
  )
}

export default Registration