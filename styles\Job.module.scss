@import "mixins";
@import "config";

.job {
  display: flex;
  flex-direction: column;
  padding: 0;

  &__inner{
    @include container();
  }

  &__head {
    width: 100%;
    padding: 32px 32px 40px 32px;
    background: $grayTone7;
    @include media(xs) {
      padding: 32px 16px;
    }

    &__container{
      @include container;
    }


    &__inner {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: calc(100% - 530px);
      padding-right: 24px;

      @include media(lg) {
        width: 100%;
      }

      @include media(sm) {
        padding-right: 0;
        align-items: center;
      }

    }

    &__top{
      margin-bottom: 32px;
    }

    &__breadcrumbs{
      font-size: 14px;
      color: $grayTone2;
      font-weight: 500;
    }



    &__bottom{
      display: flex;
      @include media(sm) {
        flex-direction: column;
        align-items: center;
      }
    }


    &__logo {
      @include media(sm) {
        margin-bottom: 12px;
      }
      img{
        border-radius: 8px;
        object-fit: cover;
        width: 100px;
        min-width: 100px;
        height: 100px;
      }
    }

    &__right {
      margin-left: 24px;
      @include media(sm) {
        margin-left: 0;
      }
    }

    &__headline {
      color: $white;
      font-family: "Poppins", sans-serif;
      font-size: 24px;
      line-height: 1;
      margin-bottom: 16px;
      font-weight: 700;
      margin-top: 8px;
      @include media(xs){
        text-align: center;
      }
    }

    &__info {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      @include media(xs) {
        flex-direction: column;
      }

      &__item {
        display: flex;
        align-items: center;
        line-height: 1.4;
        font-size: 14px;
        color: $white;
        padding: 0 10px;
        position: relative;
        @include media(xs) {
          margin-bottom: 12px;
        }

        &:after {
          content: "";
          position: absolute;
          width: 4px;
          height: 4px;
          background: $mainGreen;
          right: 0;
          top: calc(50% - 2px);
          transform: translateX(2px);
          border-radius: 50%;
          @include media(xs) {
            display: none;
          }
        }

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          &:after {
            display: none;
          }
        }

        &.gray {
          color: $grayTone2;
        }

        &.location{
          span{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 240px;
            display: block;
          }
        }
      }

      &__link{
        color: $white;

        &:hover{
          transition: .3s ease-in, .3s ease-in-out;
          color: $mainGreen
        }
      }

      &__icon {
        width: 16px;
        min-width: 16px;
        height: 16px;
        object-fit: contain;
        margin-right: 4px;
      }
    }

    &__action {
      display: flex;
      align-items: center;
    }

    &__share {
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 1;
      font-weight: 800;
      color: $white;

      img {
        width: 20px;
        min-width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    &__button {
      margin-right: 28px;
    }
  }

  &__body {
    padding: 40px 32px;
    display: flex;
    justify-content: space-between;
    @include container;
    @include media(lg) {
      flex-direction: column-reverse;
    }
    @include media(xs) {
      padding: 20px 16px;
    }
  }

  &__left {
    display: flex;
    flex-direction: column;

    &__inner {
      max-width: 642px;
    }
  }

  &__headline {
    margin-bottom: 24px;
    color: $black;
    font-size: 20px;
    line-height: 1;
    font-family: "Poppins", sans-serif;
    font-weight: 600;
  }

  &__content{
    margin-bottom: 40px;

    p{
      font-size: 14px;
      color: $black;
    }

    ul{
      margin-bottom: 12px;
      li{
        font-size: 14px;
        color: $grayTone6;
        position: relative;
        padding-left: 16px;

        &:after{
          content: "";
          display: block;
          position: absolute;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: $mainGreen;
          left: 0;
          top: 0;
          transform: translateY(6px);
        }
      }
    }
    ol{
      list-style: decimal;
      padding-left: 15px;
      margin-bottom: 12px;

      li{
        font-size: 14px;
        color: $grayTone6;
        position: relative;
      }
    }

    a{
      color: $mainGreen;
      text-decoration: underline;
    }

    h1, h2, h3, h4, h5, h6{
      line-height: 1;
      margin-bottom: 12px;
    }
  }

  &__text {
    color: $grayTone6;
    margin-bottom: 24px;
    font-size: 14px;
  }

  &__list {
    margin-bottom: 40px;

    &__item {
      padding-left: 16px;
      position: relative;
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.4;
      color: $grayTone6;

      &:after {
        content: "";
        position: absolute;
        left: 0;
        top: 5px;
        width: 8px;
        height: 8px;
        background: $mainGreen;
        border-radius: 50%;
      }
    }
  }


  &__right {
    position: relative;
    margin-left: 32px;
    @include media(lg) {
      margin: 0 0 42px 0;
    }
  }

  &__bottom {
    margin-left: 32px;
    max-width: 642px;
  }
}