import React, {useEffect, useLayoutEffect, useState} from "react";
import Header from "./Header";
import Footer from "./Footer";
import MobileMenu from "./MobileMenu";
import {useTypedSelector} from "../hook/useTypedSelector";
import HeaderInfo from "./HeaderInfo";
import axios from "axios";

const Layout = ({children}: any) => {
	const [footerData, setFooterData] = useState({});
	const mobMenu = useTypedSelector(state => state.app.visibleMobileMenu);

	useEffect(() => {
		if (mobMenu) {
			document.body.classList.add("no-scroll");
		} else {
			document.body.classList.remove("no-scroll");
		}
	}, [mobMenu]);

	useEffect(() => {
		axios.get("https://cms-dev.urecruits.com/footer").then(res => {
			setFooterData({
				addressLink: res.data.addressLink,
				addressName: res.data.addressName,
				email: res.data.email,
				phone: res.data.phone,
			});
		});
	}, []);

  return (
		<>
			<HeaderInfo/>
			<Header/>
			<main className="menu">
				{children}
			</main>
			<MobileMenu/>
			<Footer data={footerData}/>
		</>
	);
};

export default Layout;