import styles from '../styles/Header.module.scss'
import Link from 'next/link'
import { useUser } from '@auth0/nextjs-auth0'

const HeaderInfo = () => {
  const { user } = useUser()

  return (
    <div className={styles.header__info}>
      {
        user ?
          <p className={styles.header__info__text}>
            <a href="https://app.urecruits.com/"  className={styles.header__info__link}>We Are Now Live! </a> Join as an early adopter and access premium features free for 90 days.
          </p>
          :
          <p className={styles.header__info__text}>
            <Link href="/registration" ><span  className={styles.header__info__link}>We Are Now Live! </span></Link> Join as an early adopter and access premium features free for 90 days.
          </p>
      }
    </div>
  )
}

export default HeaderInfo