@import "config";
@import "mixins";

.filter {
  @include media(sm) {
    display: none;
  }

  &.active {
    display: block;
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    &__text {
      color: $grayTone6;
      font-size: 16px;
      font-weight: 800;
    }

    &__clear {
      margin-left: 10px;
      color: $greenBlue1;
      line-height: 1;
      cursor: pointer;
    }
  }

  &__range {
    &__head {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__left {
      display: flex;
    }

    &__right {
      display: flex;
    }

    &__inner{
      background: $white;
      border: 1px solid #DFE2E6;
      max-width: 68px;
      padding: 4px 8px;
      display: flex;
      align-items: center;
      border-radius: 4px;

      &.error {
        border-color: $red;
      }

      &.small {
        max-width: 44px;
        padding: 4px 2px;

        input {
          text-align: center;
        }
      }

      &.disable{
        background: #F8F9FB;
        cursor: default;
      }
    }

    &__input {
      width: 100%;
      font-size: 12px;
      text-align: start;
      line-height: 1;
      align-self: center;
    }

    &__inner{
      color: $black;
      font-size: 12px;
      line-height: 1;
      padding-right: 2px;
    }

    &__currency {

    }
  }

  &__form {
    padding: 24px;
    box-shadow: 0 4px 20px 5px rgba(153, 158, 165, 0.1);
    border-radius: 12px;
    background: #F5FCFF;

    &__item {
      width: 100%;
      margin-bottom: 24px;
      position: relative;
    }

    &__label {
      @include label();

      span {
        color: $grayTone5;
        font-weight: 500;
      }
    }

    &__input {
      @include input();
      background: $white;
      border: 1px solid #DFE2E6;
    }

    &__button {
      width: 100%;
      margin-top: 8px;
      @include media(sm) {
        font-size: 16px;
      }
    }

    &__reset {
      color: $mainGreen;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin: 0 auto;

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }

    &__checkbox {
      width: 100%;
      position: relative;

      label {
        background: $white;
        border: 1px solid $grayTone3;
        box-sizing: border-box;
        border-radius: 2px;
        width: 16px;
        min-width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      input {
        display: none;
      }

      input:checked + label {
        background: $white;
        border-color: $mainGreen;
      }

      input:checked ~ label span {
        background: url("../public/images/icon/done_ic.svg") no-repeat;
        width: 10px;
        min-width: 10px;
        height: 7px;
        background-size: contain;
        display: block;
        @include media(xs) {
          width: 13px;
          height: 10px;
        }
      }

      p {
        padding-left: 23px;
        position: absolute;
        width: calc(100% - 23px);
        margin-top: 1px;
      }
    }

    &__date {
      position: relative;
      background: $white;

      &:before {
        content: "";
        position: absolute;
        right: 0;
        top: 0;
        transform: translate(-14px, 14px);
        z-index: 0;
        width: 16px;
        height: 16px;
        background: url("../public/images/icon/calendar_ic.svg");
        background-size: contain;
      }
    }

    &__find {
      width: 24px;
      height: 24px;
      background: $greenGradient2;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 0;
      bottom: 0;
      transform: translate(-16px, -10px);
      cursor: pointer;

      img {
        width: 12px;
        height: 12px;
        object-fit: contain;
      }
    }
  }

  .range {
    height: 36px;
    display: flex;
    width: 100%;

    &__scale {
      height: 4px;
      width: 100%;
      border-radius: 4px;
      align-self: center;
    }

    &__thumb {
      height: 13px;
      width: 13px;
      border-radius: 4px;
      background: #099C73;
      box-shadow: 0 2px 4px rgba(40, 38, 61, 0.12);
      display: flex;
      justify-content: center;
      align-items: center;

      &__icon {
        height: 6px;
        width: 1px;
        background: $white;

        &.left {
          margin-right: 3px;
        }
      }
    }
  }
}