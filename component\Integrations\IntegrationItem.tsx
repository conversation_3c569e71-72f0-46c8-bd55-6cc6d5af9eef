import Image from 'next/image'
import styles from '../../styles/IntegrationsItem.module.scss'
import { IntegrationItemInterface } from '../../interfaces/HomePageInterfaces'
import emptyIcon from '../../public/images/integrations/icon17.svg'


const IntegrationItem = ({ item }:IntegrationItemInterface) => {
  return (
    <div className={styles.integrationItem}>
      <div className={styles.integrationItem__head}>
        <div className={styles.integrationItem__status}>
          {
            item.status.length !== 0 && (
              <p className={`${styles.integrationItem__status__value} ${item.status === 'Coming soon' && (styles.violet)}`}>
                {item.status}
              </p>
            )
          }
        </div>
        <div className={styles.integrationItem__image}>
          {
            item.imageSrc.src ?
              <Image src={item.imageSrc}/>
              : <Image src={emptyIcon}/>
          }
        </div>
      </div>
      <div className={styles.integrationItem__body}>
        <p className={styles.integrationItem__name}>
          {item.name}
        </p>
        <p className={styles.integrationItem__tagline}>
          {item.tagline}
        </p>
        <p className={styles.integrationItem__description}>
          {item.description}
        </p>
      </div>
    </div>
  )
}

export default IntegrationItem