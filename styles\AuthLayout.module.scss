@import "config";
@import "mixins";

.main {
  min-height: calc(100vh - 213px);
  @include media(xs) {
    padding-top: 8px;
  }
}

.header {
  padding: 40px 0;
  @include media(xs) {
    padding: 32px 0;
  }

  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
  }

  &__link {
    display: block;
    max-width: 150px;
    width: 100%;
    height: 32px;
  }
}

.footer {
  padding: 60px 0 20px 0;

  &__inner {
    @include authContainer;
  }

  &__text {
    color: $grayTone6;
    font-size: 14px;
  }
}

.mobile {
  width: 16px;
  height: 16px;
  position: relative;
  cursor: pointer;
  display: none;
  @include media(md) {
    display: block;
  }

  span {
    position: absolute;
    border-radius: 2px;
    transition: .3s cubic-bezier(.8, .5, .2, 1.4);
    width: 100%;
    height: 2px;
    transition-duration: 500ms;
    background: $grayTone5;

    &:nth-child(1) {
      top: 0;
      right: 0;
      width: 100%;
    }

    &:nth-child(2) {
      top: 7px;
      right: 0;
      opacity: 1;
      width: 90%;
    }

    &:nth-child(3) {
      bottom: 0;
      right: 0;
      width: 75%;
    }
  }

}