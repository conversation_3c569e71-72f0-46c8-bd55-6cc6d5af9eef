@import "config";
@import "mixins";

.card {
  width: calc(50% - 6px);
  padding: 24px;
  border: 1px solid $grayTone2;
  border-radius: 12px;
  background: $white;
  min-height: 382px;
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  @include media(lg) {
    width: 100%;
  }

  &.listStyle {
    width: 100%;
    min-height: auto;

    .card__bottom {
      display: none;
    }
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &__left {
      display: flex;
      align-items: center;
      @include media(xs) {
        width: 100%;
        flex-direction: column;
      }
    }

    &__right {
      display: flex;
      align-self: flex-start;
    }

    &__info {
      margin-left: 12px;
      @include media(xs) {
        margin-left: 0;
      }
    }

    &__logo {
      width: 60px;
      height: 60px;
      background: $grayTone1;
      border: 1px solid $grayTone2;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      @include media(xs) {
        margin-bottom: 16px;
        width: 80px;
        height: 80px;
      }

      img {
        width: 45px;
        height: 45px;
        min-width: 45px;
        object-fit: cover;
        @include media(xs) {
          width: 60px;
          height: 60px;
        }
      }
    }

    &__name {
      width: 100%;
      font-size: 20px;
      color: $mainGreen;
      margin-bottom: 10px;
      line-height: 1;
      font-weight: 800;
      @include media(xs) {
        text-align: center;
        display: block;
      }
    }
  }

  &__control {
    display: flex;
    align-items: center;
    cursor: pointer;

    &.active {
      .card__control__icon {
         svg{
           fill: $mainGreen;
         }
      }
      .card__control__text{
        color: $mainGreen;
      }
    }

    &.disabled{
      pointer-events: none;

      .card__control__icon{
        svg{
          path, circle{
            stroke: $grayTone5;
          }
        }
      }

      .card__control__text{
        color: $grayTone5;
      }
    }

    &:hover {
      .card__control__text {
        color: $mainGreen;
      }
    }

    &:last-child {
      margin-left: 28px;
    }

    &__icon {
      min-width: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      align-content: center;

      svg {
        path {
          stroke: $mainGreen;
        }

        circle {
          stroke: $mainGreen;
        }
      }
    }

    &__text {
      font-size: 14px;
      color: $grayTone7;
      font-weight: 800;
      padding-left: 8px;
      transition: .3s ease-in, .3s ease-in-out;
    }
  }

  &__company {
    display: flex;
    @include media(xs) {
      flex-direction: column;
    }

    &__name,
    &__location,
    &__remote {
      font-size: 14px;
      line-height: 1;
      color: $grayTone7;
      @include media(xs) {
        text-align: center;
        margin-bottom: 10px;
      }
    }

    &__name {
      margin-right: 10px;
    }

    &__location,
    &__remote {
      padding: 0 10px;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        display: block;
        width: 4px;
        height: 4px;
        background: $mainGreen;
        border-radius: 50%;
        left: -2px;
        top: calc(50% - 2px);
        @include media(xs) {
          display: none;
        }
      }

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 165px;
        display: block;
        @include media(xl) {
          max-width: 100px;
        }
        @include media(md) {
          max-width: 70px;
        }
        @include media(sm) {
          max-width: 140px;
        }
        @include media(xs) {
          max-width: 100%;
        }
      }
    }

    &__remote {
      &:after {

      }
    }

  }

  &__body {
    flex-grow: 1;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    height: 30px;
    overflow: hidden;

    &__item {
      font-size: 14px;
      color: $grayTone7;
      line-height: 1;
      margin: 0 8px 8px 0;
      padding: 8px;
      background: $grayTone2;
      border-radius: 3px;
      cursor: default;
    }
  }

  &__description {
    font-size: 14px;
    color: $grayTone6;
    margin-bottom: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-height: 20px;
    height: 60px;
  }

  &__information {
    display: flex;

    &__exp {
      margin-right: 24px;
    }

    &__exp,
    &__salary {
      font-size: 16px;
      font-weight: 800;
      color: $grayTone7;
    }
  }

  &__bottom {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    @include media(sm) {
      padding-top: 24px;
    }
    @include media(xss) {
      justify-content: space-between;
    }
  }

  &__button {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 48px;
    margin-right: 28px;
    font-size: 18px;

    &.disabled{
      pointer-events: none;
      background: linear-gradient(125.2deg, #099c73ab 8.04%, #015462a1 127.26%);
    }

    @include media(xs) {
      padding: 11px 28px;
      height: 44px;
      font-size: 16px;
    }

    @include media(xss) {
      width: 46%;
      padding: 13px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:last-child {
      margin-right: 0;
    }

    &__icon {
      width: 24px;
      min-width: 24px;
      height: 24px;
      @include media(xss) {
        width: 16px;
        min-width: 16px;
        height: 16px;
      }

      path {
        transition: .3s ease-in, .3s ease-in-out;
      }
    }

    &__text {
      margin-left: 10px;
    }

    &.save {
      &:hover {
        .card__button__icon {
          path {
            stroke: $greenBlue2;
          }
        }
      }
    }

    &.active {

      .card__button__icon {
        svg{
          fill: $mainGreen;

          path {
            stroke: $white;
          }
        }
      }
    }
  }
}