import styles from '../styles/Header.module.scss'
import globalStyle from '../styles/Global.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import { useAction } from '../hook/useAction'
import logo from '../public/images/icon/logo.svg'
import grayArrowDownIc from '../public/images/icon/solid_gray_arrow_down.svg'
import teamIc from '../public/images/icon/team_ic.svg'
import codeIc from '../public/images/icon/code_ic.svg'
import hiringIc from '../public/images/icon/screenIc.svg'
import { useUser } from '@auth0/nextjs-auth0';
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

const Header = () => {
  const { user } = useUser();
  const { toggleMenu } = useAction()
  const router = useRouter()
  const [currentPath, setCurrentPath] = useState('')

  useEffect(() => {
    if (router.asPath === '/pricing') setCurrentPath('pricing')
    if (router.asPath === '/about-us') setCurrentPath('about-us')
    if (router.asPath === '/products-and-services' || router.asPath.includes('/products-and-services/')) setCurrentPath('products-and-services')
    if (router.asPath === '/#faq') setCurrentPath('faq')
    if (router.asPath === '/') setCurrentPath('')
    if (router.asPath === '/integrations') setCurrentPath('integrations')
    if (router.asPath === '/jobs') setCurrentPath('jobs')

  }, [router.asPath])

  return (
    <header className={styles.header}>
      <div className={styles.header__inner}>
        <Link
          href={'/'}
        >
          <a className={styles.header__link}>
            <Image
              src={logo}
              className={styles.header__logo}
            />
          </a>
        </Link>
        <nav className={styles.header__nav}>
          <ul className={styles.header__list}>
            <li className={`${styles.header__list__item} ${currentPath === 'about-us' ? styles.active : ''}`}>
              <Link
                href="/about-us"
              >
                <a
                  className={styles.header__list__link}
                >
                  About us
                </a>
              </Link>
            </li>
            <li className={`${styles.header__list__item}  ${styles.services} ${currentPath === 'products-and-services' ? styles.active : ''}`}>
              <Link href='/products-and-services'>
                <a className={styles.header__list__text}>
                Products & Services
                </a>
              </Link>
              <div className={styles.header__list__arrow}>
                <Image src={grayArrowDownIc}/>
              </div>
              <ul className={styles.header__submenu}>
                <li className={`${styles.header__submenu__item} ${styles.first}`}>
                  <Link href={'/products-and-services'}>
                    <a className={styles.header__submenu__link}>
                      All products & services
                    </a>
                  </Link>
                  <p className={styles.header__submenu__description}>
                    Check out the various features to help improve your recruiting process
                  </p>
                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/recruitment-software'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={teamIc}/>
                      </div>
                      Recruitment
                    </a>
                  </Link>
                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/candidate-assessment-tool'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={codeIc}/>
                      </div>
                      Assessments & Testing
                    </a>
                  </Link>

                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/employee-background-screening-software'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={hiringIc}/>
                      </div>
                      Screening & Hiring
                    </a>
                  </Link>
                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/hr-intelligence-suite'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={hiringIc}/>
                      </div>
                      Intelligence Suite
                    </a>
                  </Link>
                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/hr-analytics-software'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={hiringIc}/>
                      </div>
                      Analytics
                    </a>
                  </Link>
                </li>
                <li className={styles.header__submenu__item}>
                  <Link href={'/products-and-services/hr-support'}>
                    <a className={styles.header__submenu__link}>
                      <div className={styles.header__submenu__icon}>
                        <Image src={hiringIc}/>
                      </div>
                      Support
                    </a>
                  </Link>
                </li>
              </ul>
            </li>
            <li className={`${styles.header__list__item} ${currentPath === 'jobs' ? styles.active : ''}`}>
              <Link
                href="/jobs"
              >
                <a
                  className={styles.header__list__link}
                >
                  Jobs
                </a>
              </Link>
            </li>
            <li className={`${styles.header__list__item} ${currentPath === 'pricing' ? styles.active : ''}`}>
              <Link
                href="/pricing"
              >
                <a
                  className={styles.header__list__link}
                >
                  Pricing
                </a>
              </Link>
            </li>
            <li className={`${styles.header__list__item} ${currentPath === 'integrations' ? styles.active : ''}`}>
              <Link
                href="/integrations"
              >
                <a
                  className={styles.header__list__link}
                >
                  Integrations
                </a>
              </Link>
            </li>

            <li className={`${styles.header__list__item} ${currentPath === 'faq' ? styles.active : ''}`}>
              <Link
                href="https://resources.urecruits.com"
              >
                <a
                  className={styles.header__list__link}
                >
                  Resources
                </a>
              </Link>
            </li>
          </ul>
        </nav>
        {
          user ?
            <div className={styles.successContainer}>
              <a href="https://app.urecruits.com/" className={styles.appLink}> Get started</a>
              <div className={styles.buttons}>
                <a href="/api/auth/logout" className={`${styles.buttons__registration} ${globalStyle.emptyButton}`}>Logout</a>
              </div>
            </div>
            :
            <div className={styles.buttons}>
              <a className={styles.buttons__login} href="/api/auth/login">
                Log in
              </a>
              <Link
                href={'/registration'}
              >
                <a className={`${styles.buttons__registration} ${globalStyle.emptyButton}`}>
                  Try it free
                </a>
              </Link>
            </div>
        }
        <div
          className={styles.mobile}
          onClick={() => toggleMenu(true)}
        >
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>
  )
}

export default Header