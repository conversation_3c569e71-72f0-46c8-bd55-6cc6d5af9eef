import '../styles/main.scss'
import type { AppProps } from 'next/app'
import { store } from '../store'
import { Provider } from 'react-redux'
import Head from 'next/head'
import favicon from '../public/favicon.png'
import { UserProvider } from '@auth0/nextjs-auth0'
import { jobPostingGoogle } from "../hook/jobPostingGoogle"
import { useRouter } from 'next/router'

function MyApp({ Component, pageProps }: AppProps) {
	const router = useRouter()
	const baseUrl = 'https://urecruits.com'
	const canonicalUrl = `${baseUrl}${router.asPath}`
	// const scriptProps: any = {
	// 	src: '//fw-cdn.com/11193446/3909870.js',
	// 	chat: 'true',
	// };

	// Define pages that should not be indexed
	const noIndexPages = [
		'/login',
		'/registration',
		'/forgot-password',
		'/reset-password',
		'/check-your-email',
		'/email-verification',
		'/empty-page'
	]

	// Determine robots meta content based on current path
	const robotsContent = noIndexPages.includes(router.pathname) 
		? "noindex, nofollow"
		: "index, follow"

	return (
		<UserProvider user={pageProps.user}>
			{/*@ts-ignore*/}
			<Provider store={store}>
				<Head>
					<link rel="shortcut icon" href={favicon.src} type="image/x-icon" />
					<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
					<meta name="robots" content={robotsContent} />
					<link rel="canonical" href={canonicalUrl} />
					<script async src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GA_TRACKING_ID}`} />
					<meta name="google-site-verification" content="UZkkk1ckUgnnhj_ORC0n3P87fe0qpNI3dr0MaFWSr6Q" />
			
					<script dangerouslySetInnerHTML={{
						__html: `window.dataLayer = window.dataLayer || [];
                      function gtag(){dataLayer.push(arguments);}
                      gtag('js', new Date());
                      gtag('config', '${process.env.GA_TRACKING_ID}', {
                      page: window.location.pathname
                      });`
					}} />
					{jobPostingGoogle(pageProps.job)}
					{/* <script async {...scriptProps}></script> */}
				</Head>
				{/*@ts-ignore*/}
				<Component {...pageProps} />
			</Provider>
		</UserProvider>
	)
}

export default MyApp
