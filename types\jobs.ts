export interface IJobs {
  limit: number,
  filters: IFilter,
  saveJob: boolean,
  subscribeJob: boolean,
  applyJob: boolean
}

export interface IFilter {
  jobTitle: string,
  jobLocation: IAsyncSelect | null
  postedOnData: null,
  salaryMonthValue: Array<number>,
  salaryYearValue: Array<number>,
  experienceYearValue: Array<number>,
  education: IReactSelectItem | null,
  skills: IAsyncSelect| null,
  companyName: IAsyncSelect | null,
  jobType: IReactSelectItem | null,
  preferableShift: IReactSelectItem| null,
  industryType: IAsyncSelect | null,
  functionalArea: IReactSelectItem | null,
}

export interface IPagination {
  limit: number
  currentPage:number,
  totalCount:number
}

export interface IReactSelectItem {
  value: string,
  label: string
}

export interface IAsyncSelect extends IReactSelectItem {
  id: number,
  map: (x:any) => any
}