import { NextComponentType } from 'next'
import styles from '../styles/HomeServices.module.scss'
import globalStyle from '../styles/Global.module.scss'
import Link from 'next/link'

import { ServicesInterface} from "../interfaces/HomePageInterfaces";
import { useUser } from '@auth0/nextjs-auth0'

type Props = {
  props: ServicesInterface
}
const HomeServices = ({props}: Props)  => {
  const { user } = useUser()

  return (
    <section className={styles.services}>
      <div className={styles.services__inner}>
        <div className={styles.services__left}>
          <span className={`${styles.services__tagline} ${globalStyle.tagline}`}>{props.tagline}</span>
          <h2 className={styles.services__headline}>
            {props.headline}
          </h2>
          <h6 className={styles.services__description}>
            {props.description}
          </h6>
          {
            user ?
              <a href="https://app.urecruits.com/" className={`${styles.services__button} ${globalStyle.filledButton}`}>
                {props.button_text}
              </a>
              :
              <Link href="/registration">
                <a className={`${styles.services__button} ${globalStyle.filledButton}`}>
                  {props.button_text}
                </a>
              </Link>
          }
        </div>
        <div className={styles.services__right}>
          {props.services_item&& props.services_item.length>0&&props.services_item.map((item,index)=>{
            return(
              <div className={styles.services__information} key={index}>
                <div className={styles.services__information__wrap}>
                  <img
                    src={item.image.url}
                    className={styles.services__information__image}
                    alt={'uRecruits'}
                  />
                  <h3 className={styles.services__information__name}>{item.headline}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {item.description}
                </p>
                <Link href={item.link_url?item.link_url:'/'}>
                  <a className={styles.services__link}>
                    {item.link_text}
                  </a>
                </Link>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default HomeServices