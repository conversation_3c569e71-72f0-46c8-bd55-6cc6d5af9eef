import styles from '../styles/PricesBanner.module.scss'
import globalStyle from '../styles/Global.module.scss'
import {PricingInterface} from "../interfaces/HomePageInterfaces";
import {NextComponentType} from "next";
type Props = {
  props: PricingInterface
}
const PricesBanner = ({props}: Props)=> {
  return (
    <section className={styles.banner}>
      <div className={styles.banner__inner}>
        <span className={`${styles.banner__tagline} ${globalStyle.tagline}`}>
          {props.tagline}
        </span>
        <h1 className={styles.banner__headline}>
          {props.headline}
        </h1>
        <p className={styles.banner__text}>
          {props.description}
        </p>
      </div>
    </section>
  )
}

export default PricesBanner