@import "config";
@import "mixins";

.popup{
  position: fixed;
  background: rgba(42, 44, 51, 0.6);
  width: 100%;
  height: 100%;
  z-index: 1000;
  left: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  display: flex;

  &__inner{
    @include container;
    display: flex;
    justify-content: center;
    @include media(xs){
      padding-left: 16px;
      padding-right: 16px;
    }
  }

  &__content{
    background: $white;
    border: 1px solid $grayTone6;
    border-radius: 12px;
    padding: 34px 32px;
    max-width: 458px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    @include media(xs){
      max-width: 344px;
    }
  }

  &__headline{
    margin-top: 32px;
    color: $grayTone7;
    @include media(xs){
      font-size:18px ;
      line-height: 1;
    }
    @include media(xss){
      font-size:16px ;
    }
  }

  &__head{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
    width: 100%;


    &__image{
      cursor: pointer;
      display: block;
      max-width: 24px;
      height: 24px;
      width: 100%;
    }
  }

  &__image{
    width: 100%;
    max-width: 110px;
    height: 110px;
    display: block;

  }

  &__description{
    margin-top: 16px;
    margin-bottom: 44px;;
    font-size: 14px;
    color: $grayTone7;
    @include media(xs){
      margin-bottom: 36px;
    }
  }

  &__button{
    @include emptyButton;
    background: $greenGradient2;
    color: $white;
  }
}