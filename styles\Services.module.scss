@import "config";
@import "mixins";

.services {
  padding-top: 140px;

  &__inner {
    @include container;
    display: flex;
    flex-direction: column;
  }

  &__headline {
    display: flex;
    color: $grayTone7;
    max-width: 400px;
    @include media(lg) {
      max-width: 100%;
    }
  }

  &__block {
    margin-bottom: 140px;
    display: flex;
    flex-direction: column;
    position: relative;
    @include media(xs) {
      margin-bottom: 100px;
    }

    &:nth-child(2) {
      &:after {
        content: '';
        position: absolute;
        left: 0;
        background: url("../public/images/icon/services-steps-green-circle_ic.svg") no-repeat;
        width: 160px;
        height: 156px;
        background-size: contain;
        transform: translate(-90px, 28px);
        z-index: -1;
        @include media(lg) {
          display: none;
        }

        @include media(xss) {
          display: flex;
          width: 85px;
          height: 62px;
          transform: translate(-32px, 90px);
        }
      }
    }

    &:first-child {
      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        background: url("../public/images/icon/plusesServiceStep1_ic.svg");
        width: 58px;
        height: 80px;
        background-size: contain;
        transform: translate(15px, -30px);
      }
    }

    &:last-child {
      margin-bottom: 0;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        background: url("../public/images/icon/plusesServiceStep2_ic.svg");
        width: 72px;
        height: 52px;
        background-size: contain;
        transform: translate(-50px, 5px);
        @include media(lg) {
          display: none;
        }

        @include media(xs) {
          display: block;
          left: unset;
          right: 0;
          top: 0;
          z-index: -1;
        }
      }
    }

    &__item {
      display: flex;
      justify-content: space-between;
      @include media(lg) {
        flex-direction: column;
      }
    }

    &__right {
      width: 60%;
      padding-top: 14px;
      display: flex;
      flex-direction: column;
      @include media(lg) {
        width: 100%;
        padding-top: 0;
      }
    }

    &__left {
      width: 40%;
      @include media(lg) {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 42px;
      }
    }
  }

  &__information {
    display: flex;
    align-items: flex-start;

    &:first-child {
      margin-bottom: 52px;
      @include media(xs) {
        margin-bottom: 38px;
      }
    }

    @include media(sm) {
      flex-direction: column;
    }

    &__wrap {
      display: flex;
      width: 100%;
      max-width: 224px;
      align-items: flex-start;
      margin-right: 28px;
      @include media(sm) {
        max-width: 100%;
        padding-right: 0;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 18px;
      }
    }

    &__image {
      display: block;
      min-width: 32px;
      width: 32px;
      height: 32px;
      @include media(xs) {
        min-width: 24px;
        width: 24px;
        height: 24px;
      }
    }

    &__name {
      color: $grayTone7;
      line-height: 1.2;
      padding-left: 20px;
      word-break: break-word;
      @include media(sm) {
        padding-left: 15px;
        line-height: 1;
      }
    }

    &__description {
      width: calc(100% - 252px);
      @include media(sm) {
        width: 100%;
      }
    }
  }
}