@import "config";
@import "mixins";

.contactUs {
  &__inner {
    @include container;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: url("../public/images/contact-us/bg.svg") no-repeat center;
    background-size: contain;
    position: relative;
    @include media(xs) {
      padding-right: 0;
      padding-left: 0;
    }

    &:before {
      display: none;
      content: "";
      position: absolute;
      background: url("../public/images/contact-us/contact-us_left_icon.svg") no-repeat;
      background-size: contain;
      width: 38px;
      height: 42px;
      left: 0;
      top: 0;
      transform: translate(23px, -36px);
      z-index: 0;
      @include media(xs) {
        display: block;
      }
    }

    &:after {
      display: none;
      content: "";
      position: absolute;
      background: url("../public/images/contact-us/contact-us_right_icon.svg") no-repeat;
      background-size: contain;
      width: 54px;
      height: 37px;
      right: 0;
      top: 0;
      transform: translate(0px, -24px);
      @include media(xs) {
        display: block;
      }
    }
  }

  &__headline {
    margin-bottom: 45px;
    @include media(sm) {
      margin-bottom: 40px;
    }
  }

  &__form {
    max-width: 644px;
    width: 100%;
    background: $milkWhite url("../public/images/icon/bg-form-contact-us_ic.svg");
    padding: 34px 40px 43px;
    border-radius: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    @include media(sm) {
      padding: 29px 32px 32px;
    }

    &__item {
      width: calc(50% - 14px);
      display: flex;
      flex-direction: column;
      margin-bottom: 25px;
      position: relative;
      @include media(sm) {
        width: 100%;
        margin-bottom: 29px;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &.error {
        .contactUs__form__input,
        .contactUs__form__textarea {
          border: 1px solid $red;
        }

        .errorMessage {
          display: block;
        }
      }
    }


    .errorMessage {
      @include error-message;
    }

    &__success{
      @include error-message;
      display: block;
      color: $mainGreen;
      transform: translateY(10px);
    }

    &__itemBig {
      width: 100%;
      margin-bottom: 32px;
      @include media(xs) {
        margin-bottom: 36px;
      }
    }

    &__label {
      font-size: 14px;
      line-height: 19px;
      font-weight: 800;
      color: $grayTone7;
      margin: 6px;
    }

    &__input,
    &__textarea {
      height: 44px;
      background: $white;
      width: 100%;
      border: 1px solid $grayTone2;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 400;
      color: $black;

      &:hover {
        border-color: $grayTone3;

        &::placeholder {
          color: $grayTone5;
        }
      }
    }

    &__textarea {
      height: 137px;
      display: flex;
      align-self: flex-start;
      resize: none;
    }

    &__button {
      margin: 0 auto;
    }
  }
}