import { NextPage } from "next";
import SupportLayout from "../../component/SupportLayout";
import { HelpCenterInterface } from "../../interfaces/HomePageInterfaces";
import styles from "../../styles/HelpCenter.module.scss";

interface Data {
  bannerData: HelpCenterInterface;
}

const Disclosure: NextPage<Data> = ({ bannerData }) => {
  return (
    <SupportLayout bannerData={bannerData.Banner}>
      <div>
        <h3 className={styles.faq__info__title}>Disclosure :</h3>
        <div
          className={styles.faq__info}
          style={{ display: "flex", flexDirection: "row" }}>
          <div
            style={{
              flex: "1",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}>
            Google APIs
          </div>

          <div className={styles.faq__info__description} style={{ flex: "3" }}>
            uRecruits use and transfer of information received from Google APIs
            to any other app will adhere to{" "}
            <a
              href={
                "https://developers.google.com/terms/api-services-user-data-policy#additional_requirements_for_specific_api_scopes"
              }
              style={{ textDecoration: "underline", color: "blue" }}>
              Google API Services User Data Policy
            </a>
            , including the Limited Use requirements. Note that apps distributed
            on Google Play are also subject to the{" "}
            <a
              style={{ textDecoration: "underline", color: "blue" }}
              href={
                "https://play.google.com/about/developer-distribution-agreement.html"
              }>
              Google Play Developer Distribution Agreement
            </a>
            .
          </div>
        </div>
        <br />
        <div
          className={styles.faq__info}
          style={{ display: "flex", flexDirection: "row" }}>
          <div
            style={{
              flex: "1",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}>
            Office 365 REST APIs
          </div>
          <div className={styles.faq__info__description} style={{ flex: "3" }}>
            By using our application's integration with Outlook and Outlook
            Calendar, you acknowledge and agree to comply with all terms and
            policies set forth by Microsoft. Please review the{" "}
            <a
              href="https://docs.microsoft.com/en-us/outlook/rest/terms-of-use"
              style={{ textDecoration: "underline", color: "blue" }}
              target="_blank"
              rel="noopener noreferrer">
              Microsoft Terms of Use
            </a>{" "}
            and{" "}
            <a
              href="https://docs.microsoft.com/en-us/office365/dev/solution-guide/office-365-rest-apis"
              style={{ textDecoration: "underline", color: "blue" }}
              target="_blank"
              rel="noopener noreferrer">
              Office 365 REST APIs
            </a>
            .
          </div>
        </div>
      </div>
    </SupportLayout>
  );
};

export default Disclosure;

export async function getServerSideProps(context: {
  res: any;
  req: any;
  query: any;
}) {
  const helpCenterPageRes = await fetch(
    "https://cms-dev.urecruits.com/help-center-page"
  );

  return {
    props: {
      bannerData: await helpCenterPageRes.json(),
    },
  };
}
