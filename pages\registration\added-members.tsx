import AuthLayout from '../../component/Auth/AuthLayout'
import styles from '../../styles/CandidateRegistration.module.scss'
import globalStyle from '../../styles/Global.module.scss'
import Image from 'next/image'
import Link from 'next/link'
import {validatePassword} from '../../hook/validatePassword'
import bg from '../../public/images/registration/figure1.svg'
import {useEffect, useState} from 'react'
import sbsPasswordCheckForErrors from '../../hook/sbsPasswordCheckForErrors'
import Head from "next/head";


const AddedMembers = () => {
  const [sbsPasswordValidation, setSbsPasswordValidation] = useState(false)
  const [disableButton, setDisableButton] = useState(false)
  const [passwordError, setPasswordError] = useState(false)
  const [confirmPasswordError, setConfirmPasswordError] = useState(false)

  const [password, setPassword] = useState('')
  const [email, setEmail] = useState('<EMAIL>')
  const [iAgree, setIAgree] = useState(false)
  const [confirmPassword, setConfirmPassword] = useState('')
  const [iAgreeError, setIAgreeError] = useState(true)
  //validate password state
  const [minLength, setMinLength] = useState(false)
  const [oneNumber, setOneNumber] = useState(false)
  const [azSmallSymbol, setAzSmallSymbol] = useState(false)
  const [azBigSymbol, setAzBigSymbol] = useState(false)
  const [specialCharacters, setSpecialCharacters] = useState(false)

  useEffect(() => {
    sbsPasswordCheckForErrors(password, setMinLength, setOneNumber, setAzSmallSymbol, setAzBigSymbol, setSpecialCharacters)
  }, [password])

  const onSubmitFormHandler = (e: any) => {
    e.preventDefault()
    validatePassword(password) ? setPasswordError(false) : setPasswordError(true)
    password !== confirmPassword ? setConfirmPasswordError(true) : setConfirmPasswordError(false)
    if (!iAgree) setIAgreeError(false)
    if (!passwordError && !confirmPasswordError && iAgree) {
      //Need to write send logic
    }
  }

  useEffect(() => {
    if (iAgree) setIAgreeError(true)
    if (!passwordError && !confirmPasswordError && iAgree && password.length > 0 && confirmPassword.length > 0) {
      setDisableButton(true)
    } else {
      setDisableButton(false)
    }
  }, [passwordError, confirmPasswordError, iAgree])


  return (
    <AuthLayout>
      <Head>
        <title>Add members | uRecruits</title>
      </Head>
      <section className={styles.registration}>
        <div className={`${styles.registration__inner} ${styles.centerPosition}`}>
          <div className={styles.registration__left}>
            <div className={`${styles.registration__left__head} ${styles.withoutList}`}>
              <span className={globalStyle.tagline}>
                company’s domain - gameofthrones.urecruits.com
              </span>
              <h2 className={styles.registration__headline}>
                Registration
              </h2>
            </div>
            <form
              className={styles.registration__form}
              onSubmit={(e) => onSubmitFormHandler(e)}
            >
              <div className={styles.registration__form__item}>
                <label className={styles.registration__form__label}>
                  Email<span> *</span>
                </label>
                <input
                  type="email"
                  placeholder="Enter company email"
                  className={`${styles.registration__form__input} ${styles.disable}`}
                  readOnly
                  value={email}
                />
                <p className={styles.errorMessage}>
                  Please enter a valid Email address
                </p>
              </div>
              <div
                className={
                  `${styles.registration__form__item} ${passwordError ? styles.passwordError : ''} 
                  ${confirmPasswordError ? styles.confirmPasswordError : ''}`
                }
              >
                <div className={styles.registration__form__container}>
                  <label className={styles.registration__form__label}>
                    Password<span> *</span>
                  </label>
                  <div className={styles.registration__validate}>
                          <span
                            className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ''} ${minLength ? styles.active : ''}`}>
                            8 min,
                          </span>
                    <span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ''}
                             ${oneNumber ? styles.active : ''}`}>
                            1 num,
                          </span>
                    <span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ''}
                             ${azSmallSymbol ? styles.active : ''}`}>
                             a-z,
                          </span>
                    <span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ''}
                             ${azBigSymbol ? styles.active : ''}`}>
                            A-Z,
                          </span>
                    <span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ''}
                             ${specialCharacters ? styles.active : ''}`}>
                            special characters
                          </span>
                  </div>
                </div>
                <input
                  type="password"
                  placeholder="Enter password"
                  className={`${styles.registration__form__input} ${styles.password}`}
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    validatePassword(e.target.value) ? setPasswordError(false) : setPasswordError(true)
                    setSbsPasswordValidation(true)
                  }}
                />
                <input
                  type="password"
                  placeholder="Confirm password"
                  className={`${styles.registration__form__input} ${styles.confirm} ${styles.confirmPassword}`}
                  value={confirmPassword}
                  onChange={(e) => {
                    setConfirmPassword(e.target.value)
                    password !== e.target.value ? setConfirmPasswordError(true) : setConfirmPasswordError(false)
                  }}
                />
                <p className={styles.errorMessage}>
                  {
                    confirmPasswordError && ('Fields must be match.')
                  }
                  {
                    passwordError && (' Password does not match validation.')
                  }
                </p>
              </div>
              <div className={`${styles.registration__form__item} ${styles.iAgree}  ${!iAgreeError ? styles.error : ''}`}>
                <div className={`${styles.registration__form__container} ${styles.rights}`}>
                  <div className={styles.registration__form__checkbox}>
                    <input
                      type="checkbox"
                      id="confirmCheckbox"
                      checked={iAgree}
                      onChange={() => {
                        setIAgree(prev => !prev)
                      }}
                    />
                    <label htmlFor="confirmCheckbox"><span/></label>
                  </div>
                  <p className={styles.registration__form__text}
                    onClick={() => {
                      setIAgree(prev => !prev)
                    }}
                  >
                    I agree with the
                    <Link href={'/terms-of-service'}>
                      <a className={styles.registration__form__link}> Terms of Service </a>
                    </Link>
                    and
                    <Link href={'/'}>
                      <a className={styles.registration__form__link}> Privacy Policy </a>
                    </Link>
                  </p>
                </div>
                <p className={styles.errorMessage}>
                  You must accept the privacy terms
                </p>
              </div>
              <div className={`${styles.registration__form__item} ${styles.buttons} ${styles.rightPosition}`}>
                <div className={styles.registration__form__container}>
                  <Link
                    href={'/'}
                  >
                    <a className={styles.registration__form__cancelLink}>
                      Cancel
                    </a>
                  </Link>
                  <button
                    className={`${styles.registration__form__submit} ${globalStyle.filledButton} ${disableButton ? '' : globalStyle.disableFilledButton} `}>
                    Submit
                  </button>
                </div>
              </div>
            </form>
          </div>
          <div className={styles.registration__right}>
            <Image src={bg}/>
          </div>
        </div>
      </section>
    </AuthLayout>
  )
}

export default AddedMembers