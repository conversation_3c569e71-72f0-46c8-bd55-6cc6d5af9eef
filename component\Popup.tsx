import {NextComponentType} from 'next'
import styles from '../styles/Popup.module.scss'
import Image from 'next/image'
import close_ic from '../public/images/icon/popup-close_ic.svg'
import success from '../public/images/icon/popup_success_ic.svg'
const Popup: NextComponentType = () => {
    return (
        <section className={styles.popup}>
            <div className={styles.popup__inner}>
                <div className={styles.popup__content}>
                    <div className={styles.popup__head}>
                        <Image
                            src={close_ic}
                            alt={'close_icon'}
                            className={styles.popup__head__image}
                        />
                    </div>
                    <Image src={success} alt='success_icon'
                           className={styles.popup__image}/>
                    <h5 className={styles.popup__headline}>
                        Thanks for the application!
                    </h5>
                    <p className={styles.popup__description}>
                        Our operators will contact you shortly
                    </p>
                    <button className={styles.popup__button}>Got it!</button>
                </div>
            </div>
        </section>
    )
}

export default Popup