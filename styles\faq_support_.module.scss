@import "mixins";
@import "config";

.start {
  display: flex;
  flex-direction: column;
}

.title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 26px;
  line-height: 1.58;
  color: $grayTone7;
  margin-bottom: 32px;
  width: 100%;
}

.contact {
  margin-bottom: 80px;
  @include media(md) {
    margin-bottom: 40px;
  }

  &__text {
    margin-left: 36px;
    display: flex;
    align-items: center;
    margin-bottom: 40px;
    font-size: 16px;
    line-height: 1.4;
    color: $grayTone6;

    svg {
      width: 20px;
      height: 20px;
      margin-right: 16px;
    }

    &.svg {
      margin-left: 0;
      margin-bottom: 32px;
    }
  }
}

.form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 564px;
  position: relative;

  @include media(lg) {
    max-width: 100%;
  }

  &__input {
    width: calc(50% - 14px);
    display: flex;
    flex-direction: column;
    margin-bottom: 28px;
position: relative;
    @include media(sm) {
      width: 100%;
      margin-bottom: 20px;
    }

    &.error{
      .errorMessage{
        display: block;
      }
    }
    &.big {
      width: 100%;
    }

    label {
      font-weight: 900;
      font-size: 14px;
      margin-bottom: 6px;
      color: $grayTone7;
    }

    textarea {
      resize: none;
      min-height: 137px;
    }

    input, textarea {
      background: $white;
      border: 1px solid rgba(223, 226, 230, 1);
      border-radius: 4px;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.7;
      transition: .3s ease-in, .3s ease-in-out;
      &.error{
        border: 1px solid $red;
        &:focus,&:hover{
          border: 1px solid $red;
        }
      }
      &:hover, &:focus {
        border: 1px solid $grayTone3;
      }

      &::placeholder {
        font-size: 14px;
        color: $grayTone4;
      }
    }
  }
}
.errorMessage{
  @include error-message;
}
.button {
  margin-top: 4px;
}

.success_message {
  @include error-message;
  display: block;
  color: $mainGreen;
  transform: translateY(10px);
}