import { NextComponentType } from 'next'
import React, { useEffect } from 'react'
import MobileMenu from '../MobileMenu'
import { useTypedSelector } from '../../hook/useTypedSelector'
import AuthHeader from './AuthHeader'
import AuthFooter from './AuthFooter'
import styles from '../../styles/AuthLayout.module.scss'


const Layout = ({ children }: any) => {
  const mobMenu = useTypedSelector(state => state.app.visibleMobileMenu)

  useEffect(() => {
    if (mobMenu) {
      document.body.classList.add("no-scroll")
    } else {
      document.body.classList.remove("no-scroll")
    }
  }, [ mobMenu])

  return (
    <>
      <AuthHeader/>
      <main className={styles.main}>
        {children}
      </main>
      <MobileMenu/>
      <AuthFooter/>
    </>
  )
}

export default Layout