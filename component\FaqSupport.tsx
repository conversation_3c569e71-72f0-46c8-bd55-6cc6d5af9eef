import {FaqSupportInterface} from "../interfaces/HomePageInterfaces";
import styles from '../styles/faq_support_.module.scss'
import {memo, useState} from "react";
import global from '../styles/Global.module.scss'
import {validateEmail} from "../hook/validateEmail";
import axios from "axios";

const FaqSupport = ({support}: { support: FaqSupportInterface }) => {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [question, setQuestion] = useState('');

    const [nameError, setNameError] = useState(false);
    const [emailError, setEmailError] = useState(false);
    const [questionError, setQuestionError] = useState(false);
    const [successSend, setSuccessSend] = useState(false);

    const formSubmit = async (e: any) => {
        e.preventDefault()
        setNameError(name === '')
        setEmailError(!validateEmail(email))
        setQuestionError(question === '')
        if (name !== '' && validateEmail(email) && question !== '') {
            const data = {
                contactForm: `
                    Name: ${name} <br>
                    Email: ${email} <br>
                    Question: ${question} <br>
                `
            }
            axios.post(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/email/contact-form`, data, {
                headers: {
                    'Content-Type': 'application/json',
                },
            }).then((res) => {
                console.log(res);
                if (res.status == 201) {
                    setSuccessSend(true)
                    // setEmail('')
                    // setQuestion('')
                    // setEmail('')
                    setTimeout(() => {
                        setSuccessSend(false)
                    }, 1500)
                }
            })
        }
    }

    return <div className={styles.start}>
        <div className={styles.contact}>
            <p className={styles.title}>{support.contact.title}</p>
            <p className={`${styles.contact__text} ${styles.svg}`}>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M18.3351 14.0999V16.5999C18.3361 16.832 18.2886 17.0617 18.1956 17.2744C18.1026 17.487 17.9662 17.6779 17.7952 17.8348C17.6242 17.9917 17.4223 18.1112 17.2024 18.1855C16.9826 18.2599 16.7496 18.2875 16.5185 18.2666C13.9542 17.988 11.491 17.1117 9.32682 15.7083C7.31334 14.4288 5.60626 12.7217 4.32682 10.7083C2.91846 8.53426 2.04202 6.05908 1.76848 3.48325C1.74766 3.25281 1.77505 3.02055 1.8489 2.80127C1.92275 2.58199 2.04146 2.38049 2.19745 2.2096C2.35345 2.03871 2.54332 1.90218 2.75498 1.80869C2.96663 1.7152 3.19543 1.6668 3.42682 1.66658H5.92682C6.33124 1.6626 6.72331 1.80582 7.02995 2.06953C7.33659 2.33324 7.53688 2.69946 7.59348 3.09992C7.699 3.89997 7.89469 4.68552 8.17682 5.44158C8.28894 5.73985 8.3132 6.06401 8.24674 6.37565C8.18028 6.68729 8.02587 6.97334 7.80182 7.19992L6.74348 8.25825C7.92978 10.3445 9.65719 12.072 11.7435 13.2583L12.8018 12.1999C13.0284 11.9759 13.3144 11.8215 13.6261 11.755C13.9377 11.6885 14.2619 11.7128 14.5601 11.8249C15.3162 12.107 16.1018 12.3027 16.9018 12.4083C17.3066 12.4654 17.6763 12.6693 17.9406 12.9812C18.2049 13.2931 18.3453 13.6912 18.3351 14.0999Z"
                        stroke="#099C73" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {support.contact.phone}
            </p>
            <p className={styles.contact__text}>
                {support.contact.email}
            </p>
            <p className={styles.contact__desc}>{support.contact.desc}</p>
        </div>
        <form className={styles.form} onSubmit={(e) => {
            formSubmit(e)
        }}>
            <p className={styles.title}>{support.supportForm.title}</p>
            <div className={`${styles.form__input} ${nameError?styles.error:''}`}>
                <label htmlFor={'name'}>{support.supportForm.nameLabel}</label>
                <input type='text' value={name} className={nameError?styles.error:''} placeholder={support.supportForm.namePlaceholder} id={'name'}
                       onChange={(e) => {
                           setName(e.target.value)
                           nameError && setNameError(e.target.value === '')
                       }}
                />
                <p className={styles.errorMessage}>
                    This is required field
                </p>
            </div>
            <div className={`${styles.form__input} ${emailError?styles.error:''}`}>
                <label htmlFor={'email'}>{support.supportForm.emailLabel}</label>
                <input type='text' value={email} className={emailError?styles.error:''} placeholder={support.supportForm.emailPlaceholder} id={'email'}
                       onChange={(e) => {
                           setEmail(e.target.value)
                           emailError && setEmailError(!validateEmail(e.target.value))
                       }}
                />
                <p className={styles.errorMessage}>
                    This is required field
                </p>
            </div>
            <div className={`${styles.form__input} ${styles.big} ${questionError?styles.error:''}`}>
                <label htmlFor={'question'}>{support.supportForm.questionLabel}</label>
                <textarea value={question} className={questionError?styles.error:''} placeholder={support.supportForm.questionPlaceholder} id={'question'}
                          onChange={(e) => {
                              setQuestion(e.target.value)
                              questionError && setQuestionError(e.target.value === '')
                          }}
                />
                <p className={styles.errorMessage}>
                    This is required field
                </p>
            </div>
            <button type={"submit"} className={`${global.filledButton} ${styles.button}`}>{support.supportForm.buttonCta}</button>
            {successSend && (<p className={styles.success_message}>You have successfully send message</p>)}
        </form>
    </div>
}
export default memo(FaqSupport)