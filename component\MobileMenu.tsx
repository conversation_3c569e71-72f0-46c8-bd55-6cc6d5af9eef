import styles from '../styles/MobileMenu.module.scss'
import globalStyle from '../styles/Global.module.scss'
import Image from 'next/image'
import Link from 'next/link'
import closeIcon from '../public/images/icon/close_ic.svg'
import { useAction } from '../hook/useAction'
import { useTypedSelector } from '../hook/useTypedSelector'
import grayArrow from '../public/images/icon/solid_gray_arrow_down.svg'
import { useState } from 'react'
import { useUser } from '@auth0/nextjs-auth0'

const MobileMenu = () => {
  const { user } = useUser()
  const { toggleMenu } = useAction()
  const mobMenu = useTypedSelector(state => state.app.visibleMobileMenu)
  const [visibleSubmenu, setVisibleSubmenu] = useState(false)

  return (
    <div className={`${styles.mobile} ${mobMenu ? styles.active : ""}`}>
      <div className={styles.mobile__head}>
        <p className={styles.mobile__title}>Menu</p>
        <div className={styles.mobile__close} onClick={() => toggleMenu(false)}>
          <Image src={closeIcon} />
        </div>
      </div>
      <div className={styles.mobile__inner}>
        <ul className={styles.mobile__list}>
          <li className={styles.mobile__item}>
            <Link href={"/about-us"}>
              <a className={styles.mobile__link} onClick={() => toggleMenu(false)}>
                About Us
              </a>
            </Link>
          </li>
          <li className={styles.mobile__item} onClick={() => setVisibleSubmenu(!visibleSubmenu)}>
            <div className={styles.mobile__item__inner}>
              <p className={`${styles.mobile__text} ${visibleSubmenu ? styles.open : ""}`}>Products & Services</p>
              <div className={`${styles.mobile__icon} ${visibleSubmenu ? styles.rotate : ""}`}>
                <Image src={grayArrow} />
              </div>
            </div>
            <ul className={`${styles.mobile__sublist} ${visibleSubmenu ? styles.display : ""}`}>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    All services
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/recruitment-software"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Recruitment
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/candidate-assessment-tool"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Assessments & Testing
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/employee-background-screening-software"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Screening & Hiring
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/hr-intelligence-suite"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Intelligence Suite
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/hr-analytics-software"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Analytics
                  </a>
                </Link>
              </li>
              <li className={styles.mobile__sublist__item}>
                <Link href={"/products-and-services/hr-support"}>
                  <a className={styles.mobile__sublist__link} onClick={() => toggleMenu(false)}>
                    Support
                  </a>
                </Link>
              </li>
            </ul>
          </li>
          <li className={styles.mobile__item}>
            <Link href={"/jobs"}>
              <a className={styles.mobile__link} onClick={() => toggleMenu(false)}>
                Jobs
              </a>
            </Link>
          </li>
          <li className={styles.mobile__item}>
            <Link href={"/pricing"}>
              <a className={styles.mobile__link} onClick={() => toggleMenu(false)}>
                Pricing
              </a>
            </Link>
          </li>
          <li className={styles.mobile__item}>
            <Link href={"/integrations"}>
              <a className={styles.mobile__link} onClick={() => toggleMenu(false)}>
                Integrations
              </a>
            </Link>
          </li>
          <li className={styles.mobile__item}>
            <Link href={"https://resources.urecruits.com"}>
              <a className={styles.mobile__link} onClick={() => toggleMenu(false)}>
                Resources
              </a>
            </Link>
          </li>
        </ul>
        {user ? (
          <div className={styles.successContainer}>
            <a href="https://app.urecruits.com/" className={styles.appLink}>
              {" "}
              Get started
            </a>
            <div className={styles.buttons}>
              <a href="/api/auth/logout" className={`${styles.buttons__registration} ${globalStyle.emptyButton}`}>
                Logout
              </a>
            </div>
          </div>
        ) : (
          <div className={styles.mobile__buttons}>
            <Link href={"/api/auth/login"}>
              <a className={styles.mobile__buttons__login} onClick={() => toggleMenu(false)}>
                Log in
              </a>
            </Link>
            <Link href={"/registration"}>
              <a className={`${styles.mobile__buttons__registration} ${globalStyle.emptyButton}`} onClick={() => toggleMenu(false)}>
                Try it free
              </a>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}

export default MobileMenu