import Layout from '../component/Layout'
import { NextPage } from 'next'
import ServicesSteps from "../component/Services/ServicesSteps";
import GetStarted from "../component/GetStarted";
import Faq from "../component/Faq";
import ContactUs from "../component/ContactUs";
import ServicesBanner from "../component/Services/ServicesBanner";
import Head from "next/head";
import {FaqInterface, ServicesPageInterface} from "../interfaces/HomePageInterfaces";

interface Data {
  faq:FaqInterface
  services:ServicesPageInterface
}
const Services: NextPage<Data> = ({faq,services}) => {
  return (
    <Layout>
      <Head>
        <title>Services | uRecruits</title>
      </Head>
        <ServicesBanner props={services}/>
        <ServicesSteps props={services}/>
        <GetStarted props={services}/>
        <Faq props={faq}/>
        <ContactUs/>
    </Layout>
  )
}

Services.getInitialProps = async () => {
  const response_faq = await fetch('https://cms-dev.urecruits.com/faq')
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const faq = await response_faq.json()
  const services = await response_services.json()
  return {faq,services}
}

export default Services