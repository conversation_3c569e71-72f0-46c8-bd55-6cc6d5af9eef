@import "mixins";
@import "config";

.popup {
  background: rgba(0, 0, 0, .5411764705882353);
  width: 100%;
  height: 100%;
  z-index: 1000;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;

  &.apply{
    .popup__body__text{
      color: $grayTone7;
    }
  }

  &__step {
    background: $white;
    border-radius: 12px;
    padding: 32px;
    max-width: 458px;
    width: 100%;
    margin: 0 16px;
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    min-height: 20px;

    &__close {
      width: 24px;
      min-width: 24px;
      height: 24px;
      object-fit: contain;
      cursor: pointer;
      margin-left: auto;

      path {
        transition: .3s ease-in, .3s ease-in-out;
      }

      &:hover {
        path {
          stroke: $red;
        }
      }
    }

    &__headline {
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-weight: 600;
      line-height: 1;
      color: $grayTone7;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 35px;

    &__text {
      font-size: 14px;

      a {
        color: $mainGreen;
      }
      &.center{
        text-align: center;
      }
    }

    &__image {
      width: 110px;
      min-width: 110px;
      height: 110px;
      object-fit: cover;
      align-self: center;
      margin-bottom: 28px;

      &.alarm {
        margin-top: 24px;
      }
    }

    &__headline {
      font-family: 'Poppins', sans-serif;
      color: $grayTone7;
      font-weight: 600;
      font-size: 20px;
      line-height: 1;
      text-align: center;
    }

    &__item {
      margin-top: 28px;
      position: relative;

      .error-message {
        @include error-message
      }

      &.error {
        .error-message {
          display: block;
        }
      }
    }

    &__label {
      @include label;
    }

    &__link{
      color: $mainGreen;
      font-size: 14px;
      font-weight: 500;
      display: block;
      text-align: center;
      text-decoration: underline;
      margin-top: 16px;
    }
  }

  &__bottom {
    display: flex;
    align-items: center;
    position: relative;

    &.end {
      justify-content: flex-end;
    }

    &.center {
      justify-content: center;
    }

    &__cancel {
      margin-right: 20px;
      font-size: 14px;
    }

    &__button{
      font-size: 14px;
    }

    .error-message {
      @include error-message;
      display: block;
      transform: translateY(8px);
    }
  }

  &__cancel {
    font-weight: 800;
    font-size: 14px;
    color: $grayTone4;
    margin-right: 25px;
    transition: .3s ease-in, .3s ease-in-out;

    &:hover {
      color: $black;
    }

    @include media(xs) {
      display: none;
    }
  }

  &__approval {
    display: flex;
    align-items: center;
    margin-right: 18px;

    &:hover {
      .popup__approval__text {
        color: $mainGreen;
      }
    }

    &__icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    &__text {
      font-size: 14px;
      font-weight: 800;
      transition: .3s ease-in, .3s ease-in-out;
      color: $grayTone7;
    }
  }
}
