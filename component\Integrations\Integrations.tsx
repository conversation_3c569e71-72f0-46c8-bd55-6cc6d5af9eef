import styles from '../../styles/Integrations.module.scss'
import IntegrationItem from './IntegrationItem'
import {integrationData} from './integrationData'
import {useEffect, useRef, useState} from 'react'
import fixedScroll from '../../hook/FixedScroll'
import {default as ReactSelect} from 'react-select'
import {selectCustomStyle} from '../../public/files/selectCustomStyle'
import {NextComponentType} from 'next'

const taglineData = [
    'Email & Calendar',
    'Assessments',
    'Video & Audio',
    'Free Job Boards',
    'Premium Job Boards',
    'Background & Drug Verification',
    'Payroll',
    'e_Signature',
    'Subscription',
    'Communication',
    'Payment',
    'Support & Help',
]

const taglineOption = [
    {value: 'All', label: 'All integrations'},
    {value: 'Email & Calendar', label: 'Email & Calendar'},
    {value: 'Assessments', label: 'Assessments'},
    {value: 'Video & Audio', label: 'Video & Audio'},
    {value: 'Free Job Boards', label: 'Free Job Boards'},
    {value: 'Premium Job Boards', label: 'Premium Job Boards'},
    {value: 'Background & Drug Verification', label: 'Background & Drug Verification'},
    {value: 'Payroll', label: 'Payroll'},
    {value: 'e_Signature', label: 'e_Signature'},
    {value: 'Subscription', label: 'Subscription'},
    {value: 'Communication', label: 'Communication'},
    {value: 'Payment', label: 'Payment'},
    {value: 'Support & Help', label: 'Support & Help'},
]

interface SearchI {
    searchValue: string
    setSearchStatus: (val: boolean) => void
    searchStatus: boolean
}

const Integrations = ({searchValue, searchStatus, setSearchStatus}: SearchI) => {

    const [currentFilter, setCurrentFilter] = useState('All')
    const menuRef = useRef<HTMLDivElement>(null)
    const [fixedMenu, setFixedMenu] = useState(false)
    const scrollRef = useRef<HTMLDivElement>(null)

    const stickyHandler = async () => {
        const sticky = await fixedScroll(scrollRef.current, menuRef.current, 100)
        setFixedMenu(() => sticky)
    }

    const wheelHandler = (e: any) => {
        e.preventDefault()
        if (scrollRef.current !== null) {
            scrollRef.current.scrollLeft += e.deltaY
        }

    }

    useEffect(() => {
        if (scrollRef.current !== null) {
            scrollRef.current.addEventListener('wheel', (e) => {
                wheelHandler(e)
            })

            window.addEventListener('scroll', stickyHandler)
        }

        return () => {
            window.removeEventListener('scroll', stickyHandler)
            window.removeEventListener('wheel', wheelHandler)
        }
    }, [])

    const filterArray = () => {
        return integrationData.filter(x => x.name.toLowerCase().includes(searchValue.toLowerCase()))
    }
    return (
        <section className={styles.integrations}>
            <div className={styles.integrations__inner}>
                <div className={styles.integrations__mobileFilter}>
                    <ReactSelect
                        options={taglineOption}
                        closeMenuOnSelect={true}
                        hideSelectedOptions={false}
                        onChange={(item: any) => {
                            setCurrentFilter(item.value)
                        }}
                        placeholder={'Select integration'}
                        defaultValue={{label: 'All integrations', value: 'All'}}
                        styles={selectCustomStyle}
                        id="integrationSelect"
                        instanceId="integrationSelect"
                    />
                </div>
                <div
                    className={`${styles.integrations__filter} ${fixedMenu ? styles.animated : ''}`}
                    id={'scroll'}
                >
                    <div className={styles.integrations__filter__inner}>
            <span className={styles.integrations__filter__tagline}>
              on this page:
            </span>
                        <div
                            ref={scrollRef}
                            className={styles.integrations__filter__items}
                        >
                            <p
                                className={`${styles.integrations__filter__link} ${currentFilter === 'All' ? styles.active : ''}`}
                                onClick={() => {
                                    setSearchStatus(false)
                                    setCurrentFilter('All')
                                }}
                            >
                                All
                            </p>
                            {
                                taglineData.map((item, index) => {
                                    return (
                                        <p
                                            className={`${styles.integrations__filter__link} ${item === currentFilter ? styles.active : ''}`}
                                            key={index}
                                            onClick={() => {
                                                setSearchStatus(false)
                                                setCurrentFilter(item)
                                            }}
                                        >
                                            {item}
                                        </p>
                                    )
                                })
                            }
                        </div>
                    </div>
                </div>
                <div>
                </div>
                <div className={styles.integrations__list} ref={menuRef}>
                    {
                        !searchStatus && integrationData.map((item, index) => {
                            if (item.tagline === currentFilter) {
                                return (
                                    <IntegrationItem
                                        key={index}
                                        item={item}
                                    />
                                )
                            } else if (currentFilter === 'All') {
                                return (
                                    <IntegrationItem
                                        key={index}
                                        item={item}
                                    />
                                )
                            }
                        })
                    }
                    {
                        searchStatus && (
                            filterArray().length > 0 ?
                                filterArray().map((item, index) => {
                                    return (
                                        <IntegrationItem
                                            key={index}
                                            item={item}
                                        />
                                    )
                                })
                                : <div className={styles.integrations__searchError}>
                                    <h2 className={styles.integrations__searchError__headline}>
                                        Sorry we couldn't find any matches for <span>{searchValue}</span>
                                    </h2>
                                    <p className={styles.integrations__searchError__tagline}>
                                        Try searching for another term
                                    </p>
                                </div>
                        )
                    }
                </div>
            </div>
        </section>
    )
}

export default Integrations