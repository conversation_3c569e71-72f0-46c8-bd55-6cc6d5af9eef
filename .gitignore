# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.idea
yarn.lock
package-lock.json
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

#sitemap
/public/robots.txt
/public/sitemap-0.xml
/public/sitemap.xml


# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
