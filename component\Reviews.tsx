import { useRef } from 'react'
import { NextComponentType } from 'next'
import { Swiper, SwiperSlide } from 'swiper/react'
import SwiperCore, { Navigation, Pagination } from 'swiper'
import styles from '../styles/Reviews.module.scss'
import globalStyle from '../styles/Global.module.scss'
import {ReviewsInterface} from "../interfaces/HomePageInterfaces";

SwiperCore.use([Navigation, Pagination])

type Props = {
  props: ReviewsInterface
}

const Reviews = ({props}: Props) => {
  const navigationPrevRef = useRef(null)
  const navigationNextRef = useRef(null)
  return (
    <section className={styles.reviews}>
      <div className={styles.reviews__inner}>
        <span className={`${styles.reviews__tagline} ${globalStyle.tagline}`}>{props.tagline}</span>
        <div className={styles.reviews__head}>
          <h2 className={styles.reviews__headline}>
            {props.headline}
          </h2>
          <div className={styles.reviews__buttons}>
            <div
              className={styles.reviews__buttons__prev}
              ref={navigationPrevRef}
            >
              <svg viewBox="0 0 32 32" fill="none">
                <path d="M21 3L10 16L21 29" stroke="#999EA5" strokeWidth="1.77778" strokeLinejoin="round"/>
              </svg>
            </div>
            <div
              ref={navigationNextRef}
              className={styles.reviews__buttons__next}
            >
              <svg viewBox="0 0 32 32" fill="none">
                <path d="M11 29L22 16L11 3" stroke="#999EA5" strokeWidth="1.77778" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
        <div className={styles.reviews__content}>
          <Swiper
            className={styles.reviews__swiper}
            slidesPerView={1}
            breakpoints={{
              991: {
                slidesPerView: 2,
              },
            }}
            loop={true}
            updateOnWindowResize={true}
            spaceBetween={28}
            navigation={{
              prevEl: navigationPrevRef.current,
              nextEl: navigationNextRef.current,
            }}
            onBeforeInit={(swiper: any) => {
              swiper.params.navigation.prevEl = navigationPrevRef.current
              swiper.params.navigation.nextEl = navigationNextRef.current
            }}
          >
            {props.reviewItem&&props.reviewItem.length>0&&props.reviewItem.map((item,index)=>{
              return(
                <SwiperSlide className={styles.reviews__swiper__slide} key={index}>
                  <div className={styles.reviews__comment}>
                    <h3 className={styles.reviews__comment__title}>
                      {item.headline}
                    </h3>
                    <p className={styles.reviews__comment__description}>
                      {item.description}
                    </p>
                  </div>
                  <div className={styles.reviews__author}>
                    <div className={styles.reviews__author__image}>
                      <img src={item.author_image.url} alt="author"/>
                    </div>
                    <div className={styles.reviews__author__info}>
                      <span className={styles.reviews__author__name}>{item.author_name}</span>
                      <span className={styles.reviews__author__work}>{item.author_position}</span>
                    </div>
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </div>
      </div>
    </section>
  )
}

export default Reviews