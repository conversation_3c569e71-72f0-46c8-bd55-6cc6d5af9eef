import type { NextPage } from 'next'
import Layout from '../component/Layout'
import Choose from '../component/Choose'
import HomeServices from '../component/HomeServices'
import AboutUs from '../component/AboutUs'
import ContactUs from '../component/ContactUs'
import Faq from '../component/Faq'
import SeeInAction from '../component/SeeInAction'
import HomeBanner from '../component/HomeBanner'
import Plans from '../component/Plans'
import Reviews from '../component/Reviews'
import Head from 'next/head'
import {
  bannerInterface,
  WhyChooseUs,
  AboutUsInterface,
  ServicesInterface,
  ReviewsInterface,
  TryToBelieveInterface,
  FaqInterface, PlanInterface
} from "../interfaces/HomePageInterfaces";

interface Data {
  data: {
    Banner: bannerInterface,
    Choose: WhyChooseUs,
    AboutUs: AboutUsInterface,
    Services:ServicesInterface,
    Reviews:ReviewsInterface,
    tryToBelieve:TryToBelieveInterface
  },
  faq:FaqInterface,
  plan: Array<PlanInterface>,
}

const Home: NextPage<Data> = ({data,faq,plan}) => {
  return (
    <Layout>
      <Head>
        <title>HR Automation Software | AI-Powered Recruiting Solutions</title>
        <meta name="description" content="Explore uRecruits' HR software solutions. Automate recruitment, streamline hiring, and leverage AI technology for faster, smarter hiring management." />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "uRecruits",
          "url": "https://urecruits.com/",
          "logo": "https://urecruits.com/_next/static/media/logo.50e35458.svg",
          "image": "https://urecruits.com/_next/static/media/logo.50e35458.svg",
          "telephone": "******-256-9676",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "490 Post St",
            "addressLocality": "San Francisco",
            "addressRegion": "CA",
            "postalCode": "94102",
            "addressCountry": "US"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": 37.7883622,
            "longitude": -122.4097873
          },
          "sameAs": [
            "https://www.linkedin.com/company/urecruits/",
            "https://www.facebook.com/uRecruits/",
            "https://www.instagram.com/ur_urecruits/",
            "https://x.com/uRecruits_Inc"
          ]
        })}} />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "What integrations does uRecruits offer?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "uRecruits connects everything—from job boards and assessments to calendars, e-signatures, and background checks. Plug into what you already use, and keep everything in one place."
              }
            },
            {
              "@type": "Question",
              "name": "What does the uRecruits setup process involve?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Go live in minutes—not weeks. All it takes is a 3-step free trial. When you're ready, pick a plan—recruitment, assessment, or the full suite. If you're unsure what fits, our team is here to help you choose the right setup for your hiring needs."
              }
            },
            {
              "@type": "Question",
              "name": "What modules are available in uRecruits?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Make things easier with recruitment, assessment, and screening modules. Upcoming: onboarding, offboarding, and full employee experience tools—all under one roof."
              }
            },
            {
              "@type": "Question",
              "name": "How do I get traction on my job openings?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "uRecruits pushes your jobs to major boards and our own job marketplace—for free. For extra visibility, boost listings and tap into our candidate database in just a few clicks."
              }
            },
            {
              "@type": "Question",
              "name": "What level of support do you offer?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "From how-to guides and videos to expert help via phone, email, or live chat—uRecruits support has your back. If you need help, our in-house team is just a call away."
              }
            }
          ]
        })}} />
      </Head>
      <HomeBanner props={data.Banner}/>
      <Choose props={data.Choose}/>
      <AboutUs props={data.AboutUs}/>
      <HomeServices props={data.Services}/>
      <Reviews props={data.Reviews}/>
      <SeeInAction props={data.tryToBelieve}/>
      <Plans props={plan}/>
      <Faq props={faq}/>
      <ContactUs/>
    </Layout>
  )
}

Home.getInitialProps = async () => {
  const response = await fetch('https://cms-dev.urecruits.com/home-page')
  const response_faq = await fetch('https://cms-dev.urecruits.com/faq')
  const response_plan = await fetch('https://cms-dev.urecruits.com/plans')
  const plan = await response_plan.json()
  const data = await response.json()
  const faq = await response_faq.json()
  return {data,faq,plan}
}
export default Home
