import Layout from "../component/Layout";
import styles from '../styles/Terms.module.scss'
import ReactMarkdown from "react-markdown";
import axios from "axios";
import rehypeRaw from "rehype-raw";

const PrivacyPolicy:any = ({data}:{data:any}) => {

	return (
		<Layout>
			<section className={styles.terms}>
				<div className={styles.terms__inner}>
					<ReactMarkdown rehypePlugins={[rehypeRaw]} components={{
						u:({node, children, ...props})=> {
							return  <u {...props} style={{textDecoration:"underline"}}>{children}</u>},
						a:({node, children, ...props})=> {
							return  <a {...props} target={'_blank'}>{children}</a>}}}>
						{data}
					</ReactMarkdown>
				</div>
			</section>

		</Layout>
	)
}
export default PrivacyPolicy

export async function getServerSideProps() {
	const data=await axios.get('https://cms-dev.urecruits.com/cancellationand-refund-policy')
	return {
		props: {
			data: data.data?.richeditor
		},
	}
}
