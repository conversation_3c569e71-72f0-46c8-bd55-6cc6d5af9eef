@import "mixins";
@import "config";

.plans {
  background: $milkWhite url("../public/images/home-banner/home-banner-bg_ic.svg");
  margin-top: 70px;
  @include media(xs){
    margin-top: 50px;
  }

  &__inner {
    @include container;
    padding: 64px 16px;
    @include media(xs) {
      padding: 32px;
    }
  }

  &__tagline {
    display: block;
    text-align: center;
  }

  &__headline {
    text-align: center;
    margin-bottom: 56px;
  }

  &__content {
    display: flex;
    width: 100%;
    align-items: flex-start;
    @include media(lg) {
      flex-direction: column;
    }
  }

  &__period {
    display: flex;
    background: $white;
    padding: 4px;
    border: 1px solid $grayTone1;
    box-sizing: border-box;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    border-radius: 35px;
    margin: 0 auto 48px auto;
    width: fit-content;

    &__item {
      display: flex;
      align-items: center;

      input {
        display: none;
        transition: .3s easy-in, .3s easy-in-out;
      }

      input:checked ~ label {
        background: $mainGreen;
        border: 1px solid $grayTone1;
        box-sizing: border-box;
        border-radius: 35px;
        font-weight: 800;
        color: $white;
      }
    }

    &__label {
      padding: 12px 28px;
      line-height: 1;
      cursor: pointer;
      @include media(md) {
        font-size: 16px;
      }

      @include media(xs) {
        font-size: 14px;
      }
    }
  }

  &__packages {
    max-width: 282px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-right: 88px;
    position: relative;
    @include media(lg) {
      margin: 0 0 32px 0;
    }

    @include media(xs) {
      justify-content: center;
      max-width: 100%;
    }

    &:after {
      content: "";
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 0;
      background: url("../public/images/icon/arrow_price.svg") no-repeat;
      background-size: contain;
      width: 294px;
      height: 354px;
      transform: translateY(46px);
      @include media(lg) {
        display: none;
      }
    }

    &__item {
      margin-bottom: 4px;
      padding: 11px 16px;
      color: $grayTone4;
      font-size: 16px;
      position: relative;
      width: 100%;
      transition: .3s ease-in, .3s ease-in-out;
      cursor: pointer;
      -webkit-tap-highlight-color: rgba(153, 158, 165, 0.01);

      @include media(xs) {
        width: fit-content;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        color: $mainGreen;
        @include media(xs) {
          color: $black;
        }
      }
    }

    &__item.active {
      background: rgba(172, 216, 209, 0.3);
      color: $mainGreen;
      border-radius: 1px 4px 4px 1px;
      @include media(xs) {
        background: unset;
        color: $black;
        border-radius: unset;
      }

      &:hover {
        color: $mainGreen;
        @include media(xs) {
          color: $black;
        }
      }

      &:after {
        content: "";
        position: absolute;
        width: 3px;
        height: 100%;
        background: $mainGreen;
        display: block;
        left: 0;
        bottom: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        @include media(xs) {
          border-top-left-radius: unset;
          border-bottom-left-radius: unset;
          width: 100%;
          height: 2px;
        }
      }
    }
  }

  &__tabs {
    width: 100%;

    &__item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      @include media(sm) {
        flex-wrap: wrap;
      }
    }
  }

  &__info {
    width: 33.33%;
    padding: 24px 22px 28px 22px;
    display: flex;
    flex-direction: column;
    background: $white;
    border-radius: 8px;
    @include media(sm) {
      width: 100%;
      margin-bottom: 36px;
    }
    @include media(xs) {
      padding: 20px 18px;
    }

    &__body {
      height: 100%;
    }

    &__name {
      color: $greenBlue1;
      font-size: 20px;
      margin-bottom: 20px;
      text-align: center;
      display: block;
      @include media(xs) {
        text-align: start;
      }
    }

    &__price {
      font-size: 48px;
      color: $black;
      text-align: center;
      font-weight: 800;
      @include media(xs) {
        font-size: 36px;
        line-height: 1;
        display: flex;
        align-items: center;
        padding-bottom: 24px;
        border-bottom: 1px solid $grayTone2;
      }

      span {
        display: none;
        font-size: 16px;
        color: $grayTone4;
        font-weight: 400;
        line-height: 1;
        @include media(xs) {
          display: block;
        }
      }
    }

    &__time {
      color: $grayTone4;
      font-size: 16px;
      padding-bottom: 24px;
      text-align: center;
      display: block;
      border-bottom: 1px solid $grayTone2;
      @include media(xs) {
        display: none;
      }
    }

    &__list {
      padding-top: 26px;
    }

    &__item {
      display: flex;
      font-size: 16px;
      color: $grayTone6;
      margin-bottom: 28px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__icon {
      margin: 2px 18px 0 0;
      min-width: 20px;
      width: 20px;
      height: 20px;
    }

    &__bottom {
      padding-top: 54px;
      flex-grow: 1;
      @include media(sm) {
        flex-grow: unset;
      }
      @include media(xs) {
        padding-top: 34px;
      }
    }

    &__button {
      display: block;
      width: max-content;
      margin: 0 auto;
      cursor: pointer;
    }

    &__more {
      display: block;
      text-align: center;
      margin-top: 12px;
      color: $mainGreen;
      transition: .3s ease-in, .3s ease-in-out;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__info.popular {
    background: $greenGradient2;
    transform: scale(1.02) translateY(-5px);
    @include media(sm) {
      transform: unset;
    }

    .plans__info__name,
    .plans__info__price,
    .plans__info__price span,
    .plans__info__time,
    .plans__info__item,
    .plans__info__more {
      color: $white;
    }

    .plans__info__button {
      background: $milkGradient;
      border: unset;
      padding: 15px 26px;
    }

  }
}