import styles from '../styles/GetStarted.module.scss'
import Link from 'next/link'
import globalStyle from '../styles/Global.module.scss'
import { ServicesPageInterface } from '../interfaces/HomePageInterfaces'
import { useUser } from '@auth0/nextjs-auth0'

type Props = {
  props: ServicesPageInterface
}

const GetStarted = ({ props }: Props) => {
  const { user } = useUser()

  const free = props.try_for_free
  return (
    <section className={styles.getStarted}>
      <div className={styles.getStarted__inner}>
        <span className={`${styles.getStarted__tagline} ${globalStyle.tagline}`}>{free.tagline}</span>
        <h2 className={styles.getStarted__headline}>
          {free.headline_first_black}<span> {free.headline_last_green}</span>
        </h2>
        <h5 className={styles.getStarted__description}>
          {free.description}<span> {free.description_bold}</span>
        </h5>
        <div className={styles.getStarted__block}>
          <div className={styles.getStarted__block__left}>
            {
              user ?
                <a href="https://app.urecruits.com/" className={`${styles.getStarted__block__buttonFree} ${globalStyle.filledButton}`}>
                  {free.button_left}
                </a>
                :
                <Link href="/registration">
                  <a className={`${styles.getStarted__block__buttonFree} ${globalStyle.filledButton}`}>{free.button_left}</a>
                </Link>
            }
            <p className={styles.getStarted__block__description}>{free.text_left}</p>
          </div>
          <div className={styles.getStarted__block__right}>
            <a href="#contactUs"
               className={`${styles.getStarted__block__buttonDemo} ${globalStyle.emptyButton}`}>{free.button_right}</a>
            <p className={styles.getStarted__block__description}>{free.button_right}</p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default GetStarted