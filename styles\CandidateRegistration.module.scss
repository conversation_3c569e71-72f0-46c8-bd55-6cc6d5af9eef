@import "mixins";
@import "config";

.customCheckbox {
  margin-right: 8px;
}

.registration {
  padding: 0;
  width: 100%;
  min-height: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  @include media(xs) {
    justify-content: flex-start;
  }

  &__progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 0 30px 0;
    position: relative;

    &:after {
      content: "";
      position: absolute;
      display: block;
      width: 100%;
      transform: translateY(-15px);
      left: 0;
      border-bottom: 2px solid rgba(255, 255, 255, 0);
      z-index: 2;
      transition: .5s ease-in, .5s ease-in-out;
    }

    &::before {
      content: "";
      position: absolute;
      display: block;
      width: calc(100% - 12px);
      transform: translateY(-15px);
      left: 0;
      border-bottom: 2px solid $grayTone3;
      transition: .5s ease-in, .5s ease-in-out;
      z-index: 2;
    }

    &__item {
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 3;

      &:first-child {
        align-items: flex-start;
      }

      &:last-child {
        align-items: flex-end;
      }
    }


    &__dots {
      min-width: 24px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-bottom: 8px;
      background: $grayTone3;
      border: 3px solid $grayTone3;
      transition: .3s ease-in, .3s ease-in-out;
    }

    &__name {
      font-size: 14px;
      color: $grayTone7;
      @include media(xs) {
        font-size: 12px;
      }
    }
  }

  &__progress.first {
    &:after {
      border-color: $mainGreen;
      width: 50%;
    }

    .registration__progress__item {
      &:first-child {
        .registration__progress__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(2) {
        .registration__progress__name {
          color: $grayTone4;
        }
      }
    }
  }

  &__progress.second {
    &:after {
      border-color: $mainGreen;
      width: 100%;
    }

    .registration__progress__item {
      &:first-child {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(2) {
        .registration__progress__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }
    }
  }

  &__progress.third {
    &:after {
      border-color: $mainGreen;
      width: 100%;
    }

    .registration__progress__item {
      &:first-child {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(2) {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }
    }
  }


  &__checkbox {
    width: 100%;
    position: relative;

    label {
      background: $white;
      border: 1px solid $grayTone3;
      box-sizing: border-box;
      border-radius: 2px;
      width: 16px;
      min-width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    input {
      display: none;
    }

    input:checked + label {
      background: $white;
      border-color: $mainGreen;
    }

    input:checked ~ label span {
      background: url("../public/images/icon/done_ic.svg") no-repeat;
      width: 10px;
      min-width: 10px;
      height: 7px;
      background-size: contain;
      display: block;
      @include media(xs) {
        width: 13px;
        height: 10px;
      }
    }

    p {
      padding-left: 23px;
      position: absolute;
      width: calc(100% - 23px);
      margin-top: 1px;
    }
  }

  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
  }

  &__inner.centerPosition {
    align-items: center;
  }

  &__left {
    max-width: 440px;
    width: 100%;
    padding-right: 24px;
    @include media(lg) {
      max-width: 600px;
      margin: 0 auto;
      padding-right: 0;
    }

    &__head {
      display: flex;
      align-items: center;
    }

    &__head.withoutList {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &__headline {
    line-height: 1;
    margin-right: 12px;
    font-weight: 500;
    @include media(xs) {
      font-size: 24px;
    }
  }

  &__option {
    display: flex;
    align-items: center;
    line-height: 1;
    position: relative;

    &:hover {
      .registration__option__list {
        display: block;
      }

      .registration__option__icon {
        transform: rotate(180deg);
      }
    }


    &__main {
      font-size: 24px;
      color: $mainGreen;
      text-transform: uppercase;
      padding: 18px 16px 10px 10px;
      cursor: pointer;
      @include media(xs) {
        font-size: 14px;
        padding: 18px 12px 10px 10px;
      }
    }

    &__line {
      height: 2px;
      width: 9px;
      background: $mainGreen;
    }

    &__icon {
      transition: .3s ease-in, .3s ease-in-out;
      min-width: 16px;
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &__list {
      display: none;
      position: absolute;
      z-index: 2;
      top: 100%;
      width: 100%;
      padding: 24px 20px;
      box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
      border: 1px solid $grayTone2;
      border-radius: 3px;
      background: $white;
      @include media(xs) {
        padding: 16px;
      }
    }

    &__item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__link {
      font-size: 24px;
      text-transform: uppercase;
      color: $grayTone5;
      line-height: 1.4;
      transition: .3s ease-in, .3s ease-in-out;
      @include media(xs) {
        font-size: 14px;
      }

      &:hover {
        color: $mainGreen;
        text-decoration: underline;
      }
    }
  }

  &__alternative {
    padding: 40px 0;
    border-bottom: 1px solid $grayTone3;
    margin-bottom: 38px;
    display: flex;
    justify-content: space-between;
    @include media(xs) {
      padding-top: 31px;
      padding-bottom: 24px;
      margin-bottom: 33px;
    }

    &__image {
      width: 100%;
      max-width: 120px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $grayTone1;
      border: 1px solid $grayTone2;
      box-sizing: border-box;
      border-radius: 4px;
      cursor: pointer;
      @include media(xs) {
        max-width: 105px;
      }
      @include media(xss) {
        max-width: 90px;
      }
    }

    &__icon {
      max-width: 24px;
      width: 100%;
      height: 24px;
    }
  }

  &__form {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    @include media(xs) {
      margin-top: 31px;
    }

    &__step {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    &__item {
      margin-bottom: 14px;
      width: 100%;
      min-width: 100%;
      position: relative;

      &__password{
        margin-top: 15px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__item.iAgree {
      margin-top: 4px;
    }

    &__domain {
      font-size: 12px;
      color: $grayTone4;

      span {
        color: $mainGreen;
      }
    }

    &__domain.error {
      span {
        color: $red;
      }
    }

    &__hidden {
      display: none;
    }

    &__item.textItem {
      margin-bottom: 30px;
    }

    &__item.short {
      width: calc(50% - 14px);
      @include media(xs) {
        width: 100%;
      }
    }

    &__item.middle {
      width: calc(65% - 14px);
      @include media(xs) {
        width: 100%;
      }
    }


    &__item.extraSmall {
      width: calc(35% - 14px);
      @include media(xs) {
        width: 100%;
      }
    }

    &__item.rightPosition {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .registration__form__container {
        margin-left: auto;
      }
    }

    &__item.buttons {
      margin-top: 7px;
      @include media(xs) {
        margin-top: 11px;
      }
    }

    &__label {
      @include label;
    }

    &__input {
      @include input;
    }

    &__input.disable {
      background: $grayTone1;
      border-color: $grayTone1;
      cursor: default;
    }

    &__item.error {
      .registration__form__input,
      .PhoneInput {
        border-color: $red;
      }


      input {
        border-color: $red;
      }

      .errorMessage {
        display: block;
      }
    }

    &__item.passwordError
    {
      .registration__form__input.password {
        border-color: $red;
      }

      .errorMessage {
        display: block;
      }
    }

    &__item.confirmPasswordError {
      .registration__form__input.confirmPassword {
        border-color: $red;
      }

      .errorMessage {
        display: block;
      }
    }

    .errorMessage,
    .authError,
    .existError {
      @include error-message;
    }

    .authError {
      display: block;
      transform: translateY(10px);
    }

    .existError {
      display: block;
    }

    &__information {
      font-size: 16px;
      color: $grayTone6;
      margin-bottom: 32px;
    }

    &__name {
      font-size: 16px;
      font-weight: 800;
      color: $mainGreen;
    }


    &__group {
      display: flex;
      margin-top: 17px;
    }

    &__date {
      position: relative;
    }

    &__calendarIc {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(-14px, 13px);
      z-index: -1;
    }

    &__radio {
      display: flex;
      align-items: center;
      margin-right: 60px;

      @include media(xs) {
        margin-right: 25px;
      }

      &:last-child {
        margin-right: 0;
      }

      input {
        cursor: pointer;
      }

      label {
        font-size: 14px;
        color: $grayTone7;
        padding-left: 7px;
        line-height: 1;
        cursor: pointer;
      }
    }

    &__container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    &__container.rights {
      justify-content: flex-start;
      flex-wrap: unset;
    }


    &__checkbox {
      @include customCheckbox;
      align-self: flex-start;
      margin-top: 2px;
    }

    &__text {
      font-size: 14px;
      color: $grayTone7;
      padding-left: 8px;
      cursor: pointer;
    }

    &__link {
      color: $mainGreen;
      transition: .3s ease-in, .3s ease-in-out;

      &:hover {
        text-decoration: underline;
      }
    }

    &__cancelLink {
      font-size: 14px;
      color: $grayTone4;
      margin-right: 32px;
      display: block;
      transition: .3s ease-in, .3s ease-in-out;
      font-weight: 800;

      &:hover {
        color: $mainGreen;
        text-decoration: underline;
      }
    }

    &__submit {
      display: block;
      margin-left: auto;
      font-size: 14px;
    }

    &__buttonBack {
      font-size: 14px;
      cursor: pointer;
    }
  }

  &__upload {
    width: 100%;

    &__inner {
      display: flex;
      justify-content: space-between;
    }

    &__button {
      width: 100%;
      padding: 9px 12px;
      background: rgba(2, 156, 165, 0.1);
      border: 1px solid $greenBlue1;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    &__icon {
      margin-right: 12px;
      display: flex;
    }

    &__text {
      font-size: 14px;
      color: $greenBlue2;
      line-height: 1;

      span {
        color: $greenBlue1;
      }
    }

    &__info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__user {
      display: flex;
      align-items: center;
      padding-right: 16px;
      cursor: pointer;
    }

    &__name {
      font-size: 12px;
      color: $grayTone7;
      line-height: 1;
      margin-left: 16px;
    }

    &__avatar {
      min-width: 72px;
      width: 72px;
      height: 72px;
    }

    &__remove {
      color: $grayTone4;
      font-size: 14px;
      font-weight: 800;
      line-height: 1;
      z-index: 5;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  &__validate {
    display: flex;
    margin-bottom: 6px;

    &__item {
      font-size: 12px;
      color: $grayTone4;
      padding-right: 4px;

      &:last-child {
        padding-right: 0;
      }
    }

    &__item.focus {
      color: $red;
    }

    &__item.active {
      color: $mainGreen;
    }

  }


  &__right {
    @include media(lg) {
      display: none;
    }
  }
}

.registration.company {
  .registration__progress.first {
    &:after {
      border-color: $mainGreen;
      width: calc(33.33% - 24px);

    }

    .registration__progress__item {
      &:first-child {
        .registration__progress__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(2),
      &:nth-child(3)
      {
        .registration__progress__name {
          color: $grayTone4;
        }
      }
    }
  }

  .registration__progress.second {
    &:after {
      border-color: $mainGreen;
      width: calc(66.66% + 36px);;
    }

    .registration__progress__item {
      &:nth-child(1) {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(2) {
        .registration__progress__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(3)
      {
        .registration__progress__name {
          color: $grayTone4;
        }
      }
    }
  }

  .registration__progress.third {
    &:after {
      border-color: $mainGreen;
      width: calc(100% - 12px);

    }

    .registration__progress__item {
      &:nth-child(1) {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }

      &:last-child
      &:nth-child(2) {
        .registration__progress__dots {
          background: $mainGreen;
          border: 3px solid $mainGreen;
        }
      }

      &:nth-child(3) {
        .registration__progress__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }
    }
  }
}

.passwordInner {
  position: relative;
  margin-bottom: 12px;

  input {
    padding-right: 32px;
  }

  .passwordType {
    width: 20px;
    height: 12px;
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(-9px, 17px);
    cursor: pointer;
  }
}