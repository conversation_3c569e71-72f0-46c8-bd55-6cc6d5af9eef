import {NextPage} from 'next'
import globalStyles from '../styles/Global.module.scss'
import styles from '../styles/ResetPassword.module.scss'
import right from '../public/images/icon/reset-password-right_ic.svg'
import success from '../public/images/icon/reset-password-success_ic.svg'
import AuthLayout from "../component/Auth/AuthLayout";
import Image from "next/image";
import Link from "next/link";
import {SyntheticEvent, useEffect, useState} from "react";

const ResetPassword: NextPage = () => {
  const [disabledButton, setDisabledButton] = useState(true)
  const [step, setStep] = useState('1')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState(false)

  useEffect(() => {
    if (password !== '' && confirmPassword !== '' && disabledButton) {
      setDisabledButton(false)
    } else {
      if (!disabledButton && password === '' && confirmPassword === '') {
        setDisabledButton(true)
      }
    }
  }, [password, confirmPassword]);

  const submitHandler=(e:SyntheticEvent):void=>{
    e.preventDefault()
    if(password===confirmPassword){
      setStep('2')
      //change password action
    }else{
      setError(true)
      setTimeout(()=>setError(false),2000)
    }
  }
  return (
    <AuthLayout>
      <section className={styles.reset}>
        <div className={styles.reset__inner}>
          {
            step==='1'?
            <div className={styles.reset__left}>
            <h2 className={styles.reset__headline}>Reset password</h2>
            <form
              onSubmit={e=>submitHandler(e)}
              className={`${styles.reset__form} ${error ? styles.error : ''}`}
            >
              <div className={styles.reset__form__text}>
                <label className={styles.reset__form__label}>Password</label>
                <span className={styles.reset__form__help}>8 min, 1 num, a-z, A-Z, special characters</span>
              </div>
              <input
                type='password'
                placeholder='Enter password'
                className={styles.reset__form__input}
                value={password}
                required
                onChange={(e)=>setPassword(e.target.value)}
              />
              <input
                type='password'
                placeholder='Confirm password'
                className={styles.reset__form__input}
                value={confirmPassword}
                onChange={(e)=>setConfirmPassword(e.target.value)}
                required
              />
              <p className={styles.errorMessage}>
                Passwords doesnt match
              </p>
              <div className={styles.reset__form__buttonWrap}>
                <button
                  type="submit"
                  className={`${disabledButton ? globalStyles.disableFilledButton : globalStyles.filledButton} ${styles.reset__form__button}`}>
                  Change password
                </button>
              </div>
            </form>
          </div>:
            null
          }
          {
            step==='2'?
              <div className={styles.reset__left}>
                <div className={styles.reset__left__image}>
                  <Image src={success} alt='success'/>
                </div>
                <h6 className={styles.reset__left__headline}>
                  Password changed successfully
                </h6>
                <p className={styles.reset__left__description}>
                  You have successfully changed your password and now you can log into your profile. Please
                  <Link href='/login'>
                    <a className={styles.reset__left__link}> Click here to Login.</a>
                  </Link>
                </p>
              </div>:
              null
          }
          <div className={styles.reset__right}>
            <Image src={right} alt='forgot-password'/>
          </div>

        </div>

      </section>
    </AuthLayout>
  )
}

export default ResetPassword