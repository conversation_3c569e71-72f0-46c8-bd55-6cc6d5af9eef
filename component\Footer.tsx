import Link from 'next/link'
import Image from 'next/image'
import styles from '../styles/Footer.module.scss'

import logo from '../public/images/icon/logo.svg'
import sendIcon from '../public/images/icon/send_ic.svg'
import locationIcon from '../public/images/icon/location_ic.svg'
import phoneIcon from '../public/images/icon/phone_ic.svg'
import mailIcon from '../public/images/icon/mail_ic.svg'
import SmoothScroll from '../hook/SmoothScroll'
import { useState } from 'react'
import { validateEmail } from '../hook/validateEmail'

const Footer = ({data}:any) => {
  const [subscribeEmail, setSubscribeEmail] = useState('')
  const [error, setError] = useState('')
  const [subscribeEmailError, setSubscribeEmailError] = useState(false)
  const [successSubscribe, setSuccessSubscribe] = useState(false)

  const onSubmitFormHandler = async (e: any) => {
    e.preventDefault()
    validateEmail(subscribeEmail) ? setSubscribeEmailError(false) : setSubscribeEmailError(true)

    if (validateEmail(subscribeEmail)) {
      setSubscribeEmailError(false)
      setError('');
      await subscribe(subscribeEmail)
    }
  }

  const subscribe = async (email:string) => {
    const res = await fetch('/api/subscribe', {
      body: JSON.stringify({
        email: email
      }),
      headers: {
        'Content-Type': 'application/json'
      },
      method: 'POST'
    });
    const { error } = await res.json();
    if (error) {
      setError('You have already subscribed to the news');
      setTimeout(() => {
        setError('')
      }, 1500)
      return;
    }
    setSubscribeEmail('')
    setSuccessSubscribe(true)

    setTimeout(() => {
      setSuccessSubscribe(false)
    }, 1500)
  };

  return (
    <footer className={styles.footer} id="anchor">
      <div className={styles.footer__inner}>
        <div className={styles.footer__top}>
          <div className={styles.footer__subscription}>
            <Link href={'/'}>
              <a className={styles.footer__subscription__logo}>
                <Image
                  src={logo}
                />
              </a>
            </Link>
            <p className={styles.footer__subscription__text}>
              Get our HR insights into your mailbox
            </p>
            <form
              className={styles.footer__subscription__form}
              onSubmit={(e) => onSubmitFormHandler(e)}
            >
              <input
                type="email"
                className={styles.footer__subscription__input}
                placeholder="Enter your email"
                value={subscribeEmail}
                onChange={(e) => {
                  setSubscribeEmail(e.target.value)
                  validateEmail(e.target.value) ? setSubscribeEmailError(false) : setSubscribeEmailError(true)
                  setSuccessSubscribe(false)
                }}
              />
              <button className={styles.footer__subscription__submit}>
                <Image
                  src={sendIcon}
                  className={styles.footer__subscription__icon}
                />
              </button>
              {subscribeEmailError && (<p className={styles.footer__subscription__error}>Email not correct</p>)}
              {error && (<p className={styles.footer__subscription__error}>{error}</p>)}
              {successSubscribe && (<p className={styles.footer__subscription__success}>You have successfully subscribed</p>)}
            </form>
          </div>
          <div className={styles.footer__nav}>
            <p className={styles.footer__nav__headline}>
              Company
            </p>
            <ul className={styles.footer__nav__list}>
              <div className={styles.footer__nav__left}>
                <li className={styles.footer__nav__item}>
                  <Link href="/about-us">
                    <a className={styles.footer__nav__link}>
                      About us
                    </a>
                  </Link>
                </li>
                <li className={styles.footer__nav__item}>
                  <Link href="/products-and-services">
                    <a className={styles.footer__nav__link}>
                      Products & Services
                    </a>
                  </Link>
                </li>
                <li className={styles.footer__nav__item}>
                  <Link href="/pricing">
                    <a className={styles.footer__nav__link}>
                      Pricing
                    </a>
                  </Link>
                </li>
              </div>
              <div className={styles.footer__nav__right}>
                <li className={styles.footer__nav__item}>
                  <Link href="https://resources.urecruits.com">
                    <a className={styles.footer__nav__link}>
                      Resources
                    </a>
                  </Link>
                </li>
                <li className={styles.footer__nav__item}>
                  <Link href="https://resources.urecruits.com/contact-3">
                    <a className={styles.footer__nav__link}>
                      Contact
                    </a>
                  </Link>
                </li>
                <li className={styles.footer__nav__item}>
                  <Link href="/disclosure">
                    <a className={styles.footer__nav__link}>
                      Disclosure
                    </a>
                  </Link>
                </li>
              </div>
            </ul>
          </div>
          <div className={styles.footer__contact}>
            <div className={styles.footer__contact__inner}>
              <p className={styles.footer__contact__headline}>
                Contact
              </p>
              <ul className={styles.footer__contact__list}>
                <li className={styles.footer__contact__item}>
                  <a
                    className={styles.footer__contact__link}
                    href={data.addressLink}
                    target="_blank"
                  >
                    <Image
                      src={locationIcon}
                      alt={'location icon'}
                      className={styles.footer__contact__image}
                    />
                    <span className={styles.footer__contact__text}>
                      {data.addressName}
                    </span>
                  </a>
                </li>
                <li className={styles.footer__contact__item}>
                  <a
                    className={styles.footer__contact__link}
                    href="tel:+14402569676"
                  >
                    <Image
                      src={phoneIcon}
                      alt={'phone icon'}
                      className={styles.footer__contact__image}
                    />
                    <span className={styles.footer__contact__text}>
                     {data.phone}
                    </span>
                  </a>
                </li>
                <li className={styles.footer__contact__item}>
                  <a
                    className={styles.footer__contact__link}
                    href="mailto:<EMAIL>"
                  >
                    <Image
                      src={mailIcon}
                      alt={'mail icon'}
                      className={styles.footer__contact__image}
                    />
                    <span className={styles.footer__contact__text}>
                      {data.email}
                    </span>
                  </a>
                </li>
              </ul>
            </div>
            <div className={styles.footer__scroll} onClick={() => SmoothScroll(0)}>
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                <rect x="0.5" y="0.5" width="63" height="63" rx="7.5" stroke="#029CA5"/>
                <path
                  d="M32 40L41.1715 40C42.9533 40 43.8457 37.8457 42.5857 36.5858L33.4142 27.4142C32.6331 26.6332 31.3668 26.6332 30.5857 27.4142L21.4142 36.5858C20.1542 37.8457 21.0466 40 22.8284 40L32 40Z"
                  fill="#029CA5"/>
              </svg>
            </div>
          </div>
        </div>
        <div className={styles.footer__bottom}>
          <div className={styles.footer__bottom__left}>
            <p className={styles.footer__bottom__copyright}>
              Copyright © {(new Date().getFullYear())} uRecruits - All Rights Reserved.
            </p>
          </div>
          <div className={styles.footer__bottom__right}>
            <ul className={styles.footer__bottom__list}>
              <li className={styles.footer__bottom__item}>
                <Link
                  href={'/privacy-policy'}
                >
                  <a className={styles.footer__bottom__link}>
                    Privacy Policy
                  </a>
                </Link>
              </li>

              <li className={styles.footer__bottom__item}>
                <Link
                  href={'/terms-of-service'}
                >
                  <a className={styles.footer__bottom__link}>
                    Terms of Service
                  </a>
                </Link>
              </li>
              <li className={styles.footer__bottom__item}>
                <Link
                  href={'/security-and-privacy'}
                >
                  <a className={styles.footer__bottom__link}>
                    Security & Privacy
                  </a>
                </Link>
              </li>
              <li className={styles.footer__bottom__item}>
                <Link
                  href={'/cancellation-and-refund-policy'}
                >
                  <a className={styles.footer__bottom__link}>
                    Cancellation & Refund Policy
                  </a>
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer