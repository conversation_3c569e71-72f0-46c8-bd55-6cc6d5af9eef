@import "config";
@import "mixins";

.table {
  padding-top: 0;
  margin-top: -415px;

  &__inner {
    @include container;
    @include media(xs) {
      padding: 0;
    }
  }

  &__content {
    display: flex;
    width: 100%;
    align-items: flex-start;
    padding-left: 32px;
    @include media(lg) {
      flex-direction: column;
    }
    @include media(sm) {
      padding-left: 0;
    }
  }

  &__content.animated {
    // position: sticky;
    top: 0;
    background: $white;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    border-radius: 0 0 16px 16px;
    padding: 0 0 0 32px;
    z-index: 10;
    align-items: center;
    @include media(xs) {
      width: 100%;
      transform: unset;
      padding: 0;
    }

    .table__info__price,
    .table__info__time,
    .table__info__list {
      display: none;
    }

    .table__tabs__item {
      padding-top: 0;
    }

    .table__info__trial {
      @include media(sm) {
        display: none;
      }
    }

    .table__info {
      margin-bottom: 0;
    }

    .table__info__button {
      @include media(sm) {
        display: none;
      }
    }

    .table__info__name {
      text-align: center;
    }

    .table__info__button.animatedButton {
      @include media(sm) {
        display: block;
      }
    }

    .table__info.popular {
      transform: scale(1.04) translateY(6px);
      border-top-left-radius: unset;
      border-top-right-radius: unset;
      z-index: 10;

      @include media(lg) {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }
      @include media(md) {
        transform: scale(1.04) translateY(3px);
        margin-bottom: 0;
      }
    }

    .table__packages {
      margin: 0;
      @include media(lg) {
        margin: 32px 0;
      }
    }

    .table__info {
      @include media(md) {
        width: 33.33%;
      }
    }

    .table__info__name {
      margin-bottom: 0;
    }

    .table__info__bottom {
      padding-top: 32px;
      @include media(sm) {
        padding-top: 16px;
      }
    }
  }

  &__container {
    background: $white;
    box-shadow: 0 4px 20px 5px rgba(153, 158, 165, 0.1);
    border-radius: 24px;
    @include media(xs) {
      padding: 0;
    }
  }

  .animatedButton {
    display: none;
  }


  &__period {
    display: flex;
    background: $white;
    padding: 4px;
    border: 1px solid $grayTone1;
    box-sizing: border-box;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    border-radius: 35px;
    margin: 0 auto 48px auto;
    width: fit-content;
    position: relative;

    &:after {
      position: absolute;
      right: 0;
      content: "";
      width: 70px;
      height: 75px;
      background: url("../public/images/icon/pricing-arrow_ic.svg");
      transform: translate(205px, 0px);

      @include media(md) {
        display: none;
      }
    }

    &__item {
      display: flex;
      align-items: center;

      input {
        display: none;
        transition: .3s easy-in, .3s easy-in-out;
      }

      input:checked ~ label {
        background: $mainGreen;
        border: 1px solid $grayTone1;
        box-sizing: border-box;
        border-radius: 35px;
        font-weight: 400;
        color: $white;
      }
    }

    &__label {
      padding: 12px 28px;
      line-height: 1;
      cursor: pointer;
      @include media(md) {
        font-size: 16px;
      }

      @include media(xs) {
        font-size: 14px;
      }
    }
  }

  &__packages {
    max-width: 282px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    margin: 105px 22px 0 0;
    position: relative;
    @include media(lg) {
      margin: 32px 0 32px 0;
    }

    @include media(lg) {
      justify-content: center;
      max-width: 100%;
    }
    @include media(xs) {
      padding: 0 32px;
    }

    &__item {
      margin-bottom: 4px;
      padding: 11px 16px;
      color: $grayTone4;
      -webkit-tap-highlight-color: rgba(153, 158, 165, 0.01);
      font-size: 16px;
      position: relative;
      width: 100%;
      transition: .3s ease-in, .3s ease-in-out;
      cursor: pointer;
      @include media(lg) {
        width: fit-content;
        margin-right: 16px;
      }

      &:last-child {
        margin-bottom: 0;
        @include media(lg) {
          margin-right: 0;
        }
      }

      &:hover {
        color: $mainGreen;
        @include media(xs) {
          color: $black;
        }
      }
    }

    &__item.active {
      background: rgba(172, 216, 209, 0.3);
      color: $mainGreen;
      border-radius: 1px 4px 4px 1px;
      @include media(lg) {
        background: unset;
        color: $black;
        border-radius: unset;
      }

      &:hover {
        color: $mainGreen;
        @include media(lg) {
          color: $black;
        }
      }

      &:after {
        content: "";
        position: absolute;
        width: 3px;
        height: 100%;
        background: $mainGreen;
        display: block;
        left: 0;
        bottom: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        @include media(lg) {
          border-top-left-radius: unset;
          border-bottom-left-radius: unset;
          width: 100%;
          height: 2px;
        }
      }
    }
  }

  &__tabs {
    width: 100%;
    @include media(xs) {
      padding: 0 16px;
    }

    &__item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding-top: 11px;
      @include media(md) {
        flex-wrap: wrap;
        padding-top: 0;
      }
    }
  }

  &__info {
    width: 33.33%;
    padding: 24px 22px 28px 22px;
    display: flex;
    flex-direction: column;
    background: $white;
    border-radius: 8px;
    @include media(md) {
      width: 100%;
      margin-bottom: 36px;
    }
    @include media(xs) {
      padding: 20px 16px;
    }

    &:last-child {
      @include media(md) {
        margin-bottom: 0;
      }
    }

    &__body {
      height: 100%;
    }

    &__trial {
      color: $greenBlue1;
      padding: 6px 12px;
      display: flex;
      margin: 24px auto;
      background: $milkWhite;
      border-radius: 3px;
      font-size: 16px;
      line-height: 1.5;
      transition: .3s ease-in, .3s ease-out;
      width: max-content;

      @include media(xs) {
        margin: 18px 0 16px;
      }

      &:hover {
        background: rgba(172, 216, 209, 0.3);
        color: $mainGreen;
      }
    }

    &__name {
      color: $greenBlue1;
      font-size: 20px;
      margin-bottom: 24px;
      text-align: center;
      display: block;

      @include media(xs) {
        text-align: start;
      }
    }

    &__price {
      font-size: 48px;
      color: $black;
      text-align: center;
      font-weight: 800;
      margin-bottom: 6px;
      @include media(xs) {
        font-size: 36px;
        line-height: 1;
        display: flex;
        align-items: center;
        padding-bottom: 24px;
      }

      span {
        display: none;
        font-size: 16px;
        color: $grayTone4;
        font-weight: 400;
        line-height: 1;
        padding-left: 6px;
        @include media(xs) {
          display: block;
        }
      }
    }

    &__time {
      color: $grayTone4;
      font-size: 16px;
      text-align: center;
      display: block;

      @include media(xs) {
        display: none;
      }
    }

    &__list {
      padding-top: 26px;
      border-top: 1px solid $grayTone2;
      @include media(xs) {
        border-top: 1px solid $grayTone2;
      }
    }

    &__item {
      display: flex;
      font-size: 16px;
      color: $grayTone6;
      margin-bottom: 28px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__icon {
      margin: 2px 18px 0 0;
      min-width: 20px;
      width: 20px;
      height: 20px;
    }

    &__bottom {
      padding-top: 54px;
      flex-grow: 1;
      @include media(sm) {
        flex-grow: unset;
      }
      @include media(xs) {
        padding-top: 34px;
      }
    }

    &__button {
      display: block;
      width: max-content;
      margin: 0 auto;
      cursor: pointer;
    }

    &__more {
      display: block;
      text-align: center;
      margin-top: 12px;
      color: $mainGreen;
      transition: .3s ease-in, .3s ease-in-out;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__info.popular {
    background: $greenGradient2;
    transform: scale(1.02) translateY(-14px);
    @include media(md) {
      transform: unset;
    }

    .table__info__name,
    .table__info__price,
    .table__info__price span,
    .table__info__time,
    .table__info__item,
    .table__info__more {
      color: $white;
    }

    .table__info__name {
      margin-top: 14px;
    }

    .table__info__button {
      background: $milkGradient;
      border: unset;
      padding: 15px 26px;
      transform: translateY(10px);
      @include media(md) {
        transform: translateY(0);
      }
    }

    .table__info__trial {
      &:hover {
        color: #015462;
        background-color: $white;
      }
    }
  }

  &__detail {
    margin-top: 28px;
    padding-bottom: 32px;
    @include media(xs) {
      margin-top: 16px;
    }

    &__item {
      padding-bottom: 24px;
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;

      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        border-bottom: 1px solid $grayTone2;
        width: calc(100% - 64px);
        margin: 0 auto;
        align-self: center;
        @include media(xs) {
          width: 100%;
        }
      }

      &:last-child {
        &:after {
          display: none;
        }
      }
    }

    &__item.hide {
      .table__detail__head {
        padding: 24px 32px;
      }

      .table__detail__title {
        color: $black;
      }

      .table__detail__arrow {
        transform: rotate(0);

        svg {
          path {
            stroke: $mainGreen;
          }
        }
      }
    }

    &__head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 32px 0 32px;
      cursor: pointer;
    }

    &__title {
      font-size: 20px;
      font-weight: 800;
      color: $grayTone4;
      padding-right: 20px;
    }

    &__arrow {
      transform: rotate(-180deg);
      cursor: pointer;
      width: 24px;
      min-width: 24px;
      height: 24px;
      object-fit: contain;
      transition: .3s ease-in, .3s ease-in-out;

      svg {
        path {
          stroke: $grayTone4;
        }
      }
    }
  }


  &__list {
    &__item {
      display: flex;
      width: 100%;
      padding: 18px 32px;
      border-radius: 8px;
      background: $white;
      align-items: center;
      @include media(lg) {
        flex-wrap: wrap;
        padding: 7px 0;
      }

      &:nth-last-child(odd) {
        background: $grayTone1;
        @include media(lg) {
          background: unset;
        }
      }
    }

    &__image {
      height: 20px;
      width: 20px;
    }

    &__cell {
      display: flex;
      justify-content: left;
      flex: 1;
      color: $grayTone6;
      font-size: 16px;
      padding-left: 100px;

      &:first-of-type {
        padding-left:0;
      }

      @include media(lg) {
        width: 33.33%;
        flex: unset;
        padding: 12px 32px;
      }

      @include media(xs) {
        font-size: 14px;
      }

      &:first-child {
        justify-content: flex-start;
        @include media(lg) {
          width: 100%;
          background: $grayTone1;
          margin-bottom: 14px;
        }
      }
    }
  }


}