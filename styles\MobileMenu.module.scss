@import "config";
@import "mixins";

.mobile {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99;
  background: #fff;
  width: 100%;
  height: 100vh;
  overflow: auto;
  opacity: 0;
  transform: translateY(-100%);
  transition: transform .4s linear;

  &__inner {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100vh - 120px);
    padding: 0 32px;
  }

  &__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px;
  }

  &__title {
    font-weight: 800;
    line-height: 1;
    color: $black;
    font-size: 18px;
  }

  &__close {
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__list {
    display: flex;
    flex-direction: column;
  }

  &__item {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;

    &__inner {
      width: 100%;
      display: flex;
      align-items: center;
    }
  }

  &__link,
  &__text {
    color: $grayTone6;
    font-size: 18px;
    display: block;
    width: 100%;
    line-height: 1;
  }

  &__text {
    width: fit-content;
    position: relative;
  }

  &__text.open {
    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      transform: translateY(2px);
      border-bottom: 2px solid $mainGreen;
      width: 100%;
    }
  }

  &__icon {
    margin-left: 4px;
    transition: .3s ease-in, .3s ease-in-out;
  }

  &__icon.rotate {
    transform: rotate(180deg);
  }

  &__sublist {
    margin-top: 20px;
    padding-left: 16px;
    display: none;
    transition: .3s ease-in, .3s ease-in-out;

    &__item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__link {
      line-height: 1;
      color: $grayTone6;
    }
  }

  &__sublist.display {
    display: block;
  }

  &__buttons {
    padding: 80px 0 60px 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;

    @include ios {
      padding: 80px 0 100px 0;
    }

    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(0px, 5px);
      background: url("../public/images/mobile-menu/arrow.svg");
      background-size: contain;
      width: 66px;
      height: 143px;
    }

    &__login {
      padding-bottom: 36px;
      font-size: 18px;
      font-weight: 500;
      color: $grayTone6;
      width: fit-content;
    }
  }

  .successContainer {
    display: flex;
    flex-direction: column;
  }

  .appLink {
    color: $mainGreen;
    font-weight: 600;
    line-height: 1.5;
    transition: .3s ease-in, .3s ease-in-out;
    margin-bottom: 32px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.mobile.active {
  transform: translateY(0);
  opacity: 1;
}