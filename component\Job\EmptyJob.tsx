import emptyImg from '../../public/images/empty-job.svg'
import Link from "next/link";
import styles from '../../styles/EmptyJob.module.scss'
import globalStyles from '../../styles/Global.module.scss'

const EmptyJob = () => {

  return (
    <div className={styles.empty}>
      <div className={styles.empty__inner}>
        <div className={styles.empty__content}>
          <img src={emptyImg.src} alt="empty" className={styles.empty__img}/>
          <p className={styles.empty__headline}>
            Ooops!
          </p>
          <p className={styles.empty__description}>
            Looks like what you are looking for has been deleted.
          </p>
          <Link href="/jobs" >
            <a className={`${styles.empty__button} ${globalStyles.filledButton}`}>
              Back to all jobs
            </a>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default EmptyJob