import Link from 'next/link'
import styles from '../styles/Choose.module.scss'
import globalStyle from '../styles/Global.module.scss'
import { WhyChooseUs, WhyChooseUsItem } from '../interfaces/HomePageInterfaces'

type Props = {
  props: WhyChooseUs
}
const Choose = ({props}: Props) => {
  const renderItems = (items: WhyChooseUsItem[]) => {
    return items.map((item, index) => {
      return (
          <li className={styles.choose__item} key={index}>
            <Link
                href={item.link_url}
            >
              <a className={styles.choose__item__link}>
                <img src={item.image.url} alt="image"/>
                <h3 className={styles.choose__item__name}>
                  {item.headline}
                </h3>
                <div className={styles.choose__item__information}>
                  <p className={styles.choose__item__description}>
                    {item.description}
                  </p>
                  <p className={styles.choose__item__more}>
                    {item.link_text}
                  </p>
                </div>
              </a>
            </Link>
          </li>
      )
    })
  }

  return (
    <section className={styles.choose}>
      <div className={styles.choose__inner}>
        <p className={`${styles.choose__tagline} ${globalStyle.tagline}`}>
          {props.tagline}
        </p>
        <h2 className={styles.choose__headline}>
          {props.headline}
        </h2>
        <ul className={styles.choose__list}>
          {renderItems(props.choose_item)}
        </ul>
      </div>
    </section>
  )
}

export default Choose