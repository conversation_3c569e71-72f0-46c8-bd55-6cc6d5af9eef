@import "mixins";
@import "config";

.action {
  display: flex;
  @include media(xs) {
    flex-direction: column;
    align-items: center;
  }

  &.black {
    margin-top: 32px;

    .action__item {
      color: $grayTone6;

      &:first-child {
        color: $white;
        font-size: 14px;
      }
    }
  }

  &__item {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 1;
    font-weight: 800;
    color: $white;
    margin-right: 28px;
    position: relative;
    @include media(xs) {
      margin: 0 0 16px 0;
      &:last-child {
        margin-bottom: 0;
      }
    }

    &.active{
      svg{
        stroke: $mainGreen;
        fill: $mainGreen;
        path{
          stroke: $mainGreen;
        }
      }
    }

    &.disabled{
      pointer-events: none;
      background: $grayTone5;
    }

    svg,
    img {
      width: 20px;
      min-width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    &.applyBtn {
      padding: 12px 28px;
    }
  }

  &__notification {
    position: absolute;
    top: 0;
    transform: translateY(-16px);
    font-size: 14px;
    width: 90px;
    font-weight: 400;
    color: $black;
    box-shadow: 0 0 8px rgba(13, 16, 37, 0.15);
    background: $white;
    border-radius: 4px;
    padding: 5px 0;
  }
}