import styles from '../styles/HelpCenter.module.scss'
import globalStyle from '../styles/Global.module.scss'
import {HelpCenterInterface} from "../interfaces/HomePageInterfaces";
import {useState} from "react";

const search = require('../public/images/help-center/search_ic.svg')

type Props = {
  props: HelpCenterInterface,
  setFilter:(val:string)=>void
}

const PricesBanner = ({props,setFilter}: Props) => {
  const [searchState, setSearchState] = useState('')

  const submitSearchHandler=(e:any)=>{
    e.preventDefault()
    setFilter(searchState)
  }
  return (
    <section className={styles.banner}>
      <div className={styles.banner__inner}>
        <span className={`${styles.banner__tagline} ${globalStyle.tagline}`}>
          {props.Banner.tagline}
        </span>
        <h1 className={styles.banner__headline}>
          {props.Banner.headline}
        </h1>
        <p className={styles.banner__text}>
          {props.Banner.description}
        </p>
        <label className={styles.banner__label} htmlFor='search'>
          <input className={styles.banner__input} placeholder='Search...' id='search' value={searchState} onChange={e=>setSearchState(e.target.value)}/>
          <img src={search.default.src} className={styles.banner__image} onClick={(e)=>submitSearchHandler(e)}/>
        </label>

      </div>
    </section>
  )
}

export default PricesBanner