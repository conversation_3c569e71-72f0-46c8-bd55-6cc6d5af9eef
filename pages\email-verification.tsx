import Head from "next/head";
import Layout from "../component/Layout";
import globalStyle from "../styles/Global.module.scss";
import styles from "../styles/EmailVerification.module.scss";
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { FormEvent, useEffect, useState } from "react";
import {validatePassword} from "../hook/validatePassword";
import hidePasswordIc from "../public/images/icon/hide_password_ic.svg";
import displayPasswordIc from "../public/images/icon/open_password_ic.svg";

interface IProps {
	ticket: string,
	candidate:boolean
}

const EmailVerificationPage = ({ticket,candidate}:IProps) => {
	const [error, setError] = useState(false);
	const [confirmError, setConfirmError] = useState(false);
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [displayNewPassword, setDisplayNewPassword] = useState(false);
	const [displayConfirmPassword, setDisplayConfirmPassword] = useState(false);
	useEffect(() => {
		confirmPassword === password ? setConfirmError(false) : setConfirmError(true);
	}, [password, confirmPassword]);

	const submitForm = (e:FormEvent) => {
		e.preventDefault();
		if (!error && !confirmError) {
			const data = {
				"inviteLink": ticket,
				"newPassword": password
			}
			axios.patch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/${candidate ? "candidate":"recruiter"}/verification`,data, {
				headers: {
					'Content-Type': 'application/json',
				},
			}).then((res) => {
				if (res.status == 200) {
					location.replace('/api/auth/login')
				}
			})
		}
	}

	return (
		<Layout>
			<Head>
				<title>Email Verification | uRecruits</title>
			</Head>
			<section>
				<div className={styles.inner}>
					<form action="#" className={styles.form} onSubmit={(e)=>submitForm(e)}>
						<div>
							<div
								className={`${styles.registration__form__item}  ${confirmError
									? styles.confirmPasswordError
									: ""}`}>

								<div className={styles.form__container}>
									<label className={styles.form__label}>
										Password<span> *</span>
									</label>
								</div>
								<div className={styles.passwordInner}>
																	<input
										type={displayNewPassword ? "text" : "password"}
										autoComplete="new-password"
										placeholder="Enter password"
										className={`${styles.form__input} ${error && styles.error}`}
										pattern="(?=^.{8,}$)((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$"
										required={true}
										value={password}
										onChange={(e) => {
											setError(false);
											setPassword(e.target.value);
										}}
										onInvalid={(e) => {
											e.preventDefault();
											setError(true);
										}}
									/>
									{
										!displayNewPassword ? (
											<img
												src={hidePasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayNewPassword(true)}
											/>
										) : (
											<img
												src={displayPasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayNewPassword(false)}
											/>
										)
									}
								</div>
								<div className={styles.passwordInner}>
									<input
										type={displayConfirmPassword ? "text" : "password"}
											autoComplete="new-password"
											placeholder="Confirm password"
										className={`${styles.form__input}  ${confirmError && styles.error}`}
										required={true}
										onChange={(e) => {setConfirmPassword(e.target.value)}}
										value={confirmPassword}
										onInvalid={(e) => {
											e.preventDefault();
											setConfirmError(true);
										}}
									/>
									{
										!displayConfirmPassword ? (
											<img
												src={hidePasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayConfirmPassword(true)}
											/>
										) : (
											<img
												src={displayPasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayConfirmPassword(false)}
											/>
										)
									}
								</div>
								<p className={styles.errorMessage}>
									{
										confirmError && ('Password does not match')
									}
									{
										error && ('Password must contain the following: Minimum 8 characters; At least 1 number; At lease one special symbol; Only one or more Capital letter; Only one or more lowercase letter')
									}
								</p>
							</div>





						</div>
						<div className={`${styles.form__item} ${styles.form__buttons}`}>
							<button
								className={`${styles.form__submit} ${globalStyle.filledButton}`}>
								Activate account
							</button>
						</div>
					</form>
				</div>
			</section>
		</Layout>
	);
};

export default EmailVerificationPage;

export async function getServerSideProps (context: { query: { ticket: string,candidate:boolean }, res: NextResponse, req: NextRequest }) {
	const { data } = await axios.get(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/${context.query.hasOwnProperty('candidate') ? 'candidate':'recruiter'}/check-verification/${context.query.ticket}`);
	if (!data) {
		return {
			redirect: {
				destination: "/",
				permanent: false,
			},
		};
	}
	return {
		props: {
			ticket: context.query.ticket,
			candidate: context.query.hasOwnProperty('candidate') ?true:false,
		},
	};
}