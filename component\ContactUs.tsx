import styles from '../styles/ContactUs.module.scss'
import { useState } from 'react'
import globalStyle from '../styles/Global.module.scss'
import { validateEmail } from '../hook/validateEmail'
import axios from "axios";

const ContactUs = () => {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [businessUrl, setBusinessUrl] = useState('')
  const [employeesNumber, setEmployeesNumber] = useState('')
  const [message, setMessage] = useState('')

  const [nameError, setNameError] = useState(false)
  const [emailError, setEmailError] = useState(false)
  const [messageError, setMessageError] = useState(false)
  const [successSend, setSuccessSend] = useState(false)

  const validateForm = (): boolean => {
    return name.length !== 0 && validateEmail(email) && message.length !== 0
  }

  const clearInputFields = (): void => {
    setName('')
    setEmail('')
    setBusinessUrl('')
    setEmployeesNumber('')
    setMessage('')
  }

  const onSubmitFormHandler = (e: any) => {
    e.preventDefault()
    name.length === 0 && (setNameError(true))
    message.length === 0 && (setMessageError(true))
    validateEmail(email) ? setEmailError(false) : setEmailError(true)

    if (validateForm()) {
      const data = {
        contactForm: `Name: ${name} <br>
          Email: ${email} <br>
          Business url: ${businessUrl} <br>
          Number of employees: ${employeesNumber} <br>
          Message: ${message}  <br>
        `
      }
      axios.post(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/email/contact-form`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => {
        if (res.status == 201) {
          setSuccessSend(true)
          clearInputFields()
          setTimeout(() => {
            setSuccessSend(false)
          }, 1500)
        }
      })
    }
  }

  return (
    <section className={styles.contactUs} id="contactUs">
      <div className={styles.contactUs__inner}>
        <span className={`${styles.contactUs__tagline} ${globalStyle.tagline}`}>Contact us</span>
        <h2 className={styles.contactUs__headline}>Get in touch!</h2>
        <form className={styles.contactUs__form} onSubmit={(e) => onSubmitFormHandler(e)}>
          <div className={`${styles.contactUs__form__item} ${nameError ? styles.error : ''}`}>
            <label
              htmlFor="name"
              className={styles.contactUs__form__label}
            >
              Name
            </label>
            <input
              id="name"
              type="text"
              className={styles.contactUs__form__input}
              placeholder="Enter your name"
              value={name}
              onChange={(e) => {
                setName(e.target.value)
                e.target.value.length === 0 ? setNameError(true) : setNameError(false)
              }}
            />
            <p className={styles.errorMessage}>
              This is requred field
            </p>
          </div>
          <div className={`${styles.contactUs__form__item}  ${emailError ? styles.error : ''}`}>
            <label
              htmlFor="email"
              className={styles.contactUs__form__label}
            >
              Email
            </label>
            <input
              id="email"
              type="text"
              className={styles.contactUs__form__input}
              placeholder="Enter your email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                validateEmail(e.target.value) ? setEmailError(false) : setEmailError(true)
              }}
            />
            <p className={styles.errorMessage}>
              Please enter a valid Email address
            </p>
          </div>
          <div className={styles.contactUs__form__item}>
            <label
              htmlFor="url"
              className={styles.contactUs__form__label}
            >
              Business url
            </label>
            <input
              id="url"
              type="text"
              className={styles.contactUs__form__input}
              placeholder="Enter your business url"
              value={businessUrl}
              onChange={(e) => {
                setBusinessUrl(e.target.value)
              }}
            />
            <p className={styles.errorMessage}>
              This is requred field
            </p>
          </div>
          <div className={styles.contactUs__form__item}>
            <label
              htmlFor="employees"
              className={styles.contactUs__form__label}
            >
              Number of employees
            </label>
            <input
              id="employees"
              type="text"
              className={styles.contactUs__form__input}
              placeholder="Choose number of employees"
              value={employeesNumber}
              onChange={(e) => {
                setEmployeesNumber(e.target.value)
              }}
            />
          </div>
          <div className={`${styles.contactUs__form__item} ${styles.contactUs__form__itemBig}  ${messageError ? styles.error : ''}`}>
            <label
              htmlFor="message"
              className={styles.contactUs__form__label}
            >
              Message
            </label>
            <textarea
              id="message"
              className={styles.contactUs__form__textarea}
              placeholder="Tell us more about HR needs."
              value={message}
              onChange={(e) => {
                setMessage(e.target.value)
                e.target.value.length === 0 ? setMessageError(true) : setMessageError(false)
              }}
            />
            <p className={styles.errorMessage}>
              This is requred field
            </p>
          </div>
          <div className={`${styles.contactUs__form__item} ${styles.contactUs__form__itemBig}`}>
            <button type="submit" className={`${styles.contactUs__form__button} ${globalStyle.filledButton}`}>
              Get a quote
            </button>
            {successSend && (<p className={styles.contactUs__form__success}>You have successfully send message</p>)}
          </div>
        </form>
      </div>
    </section>
  )
}

export default ContactUs