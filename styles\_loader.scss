@keyframes ldio-dgf5f8tz80f {
  0% {
    transform: translate(-50%, -50%) rotate (0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.ldio-dgf5f8tz80f div {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 20px solid #099c73;
  border-top-color: transparent;
  border-radius: 50%;
}

.ldio-dgf5f8tz80f div {
  animation: ldio-dgf5f8tz80f 1s linear infinite;
  top: 100px;
  left: 100px
}

.loadingio-spinner-rolling-osyur32v9lh {
  width: 200px;
  height: 200px;
  display: inline-block;
  overflow: hidden;
  background: #ffffff;
}

.ldio-dgf5f8tz80f {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(1);
  backface-visibility: hidden;
  transform-origin: 0 0; /* see note above */
}

.ldio-dgf5f8tz80f div {
  box-sizing: content-box;
}
