import Link from 'next/link'
import styles from '../styles/Faq.module.scss'
import FaqItem from './FaqItem'
import globalStyle from '../styles/Global.module.scss'
import {FaqInterface} from "../interfaces/HomePageInterfaces";

type Props = {
  props: FaqInterface
}
const Faq = ({props}: Props) => {
  return (
    <section className={styles.faq} id="faq">
      <div className={styles.faq__inner}>
        <span className={`${styles.faq__tagline} ${globalStyle.tagline}`}>
          {props.tagline}<span>{props.tagline_after}</span>
        </span>
        <h2 className={styles.faq__headline}>
          {props.headline}
        </h2>
        <div className={styles.faq__container}>
          <ul className={styles.faq__list}>
            {
              props.faqitem && props.faqitem.length > 5 ? <>
                  <FaqItem item={props.faqitem[0]}/>
                  <FaqItem item={props.faqitem[1]}/>
                  <FaqItem item={props.faqitem[2]}/>
                  <FaqItem item={props.faqitem[3]}/>
                  <FaqItem item={props.faqitem[4]}/>
                </>
                :
                props.faqitem.map((item, index) => {
                  return (
                    <FaqItem item={item} key={index}/>
                  )
                })
            }
          </ul>
          <Link
            href={'/faq'}
          >
            <a className={`${styles.faq__all} ${globalStyle.emptyButton}`}>
              See all FAQs
            </a>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default Faq