import {memo} from "react";
import styles from '../../styles/articles-theme.module.scss'

export default memo(({title, desc, quantity, updatedAt, titleFont}:
{ title: string, desc: string, quantity: number, updatedAt?: any, titleFont: number }) => {
	return <>
		<h3 className={styles.content_box__title} style={{fontSize:titleFont+'px'}}>{title}</h3>
		<p className={styles.content_box__desc}>{desc}</p>
		<div className={styles.content_box__bottom_block}>
			<Icon/>
			<div className={styles.information_block}>
				{
					!updatedAt?<>
						<p className={`${styles.information_block__text} ${styles.bold}`}>{quantity} topic(s) on this collection</p>
							<p className={styles.information_block__text}>Written by <span>URecruits team</span></p>
						</>:
						<>
							<p className={`${styles.information_block__text}`}>Updated at {new Date(updatedAt).toLocaleDateString('en',{day:'numeric',year:'numeric',month:'short'})}</p>
							<p className={styles.information_block__text}>Written by <span>URecruits team</span></p>
						</>
				}
			</div>
		</div>
	</>
})

const Icon = ()=>{
	return <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
			<mask id="mask0_590_41297" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="40" height="40">
			<circle cx="20" cy="20" r="20" fill="#D3DCE6"/>
		</mask>
		<g mask="url(#mask0_590_41297)">
		</g>
		<g clipPath="url(#clip0_590_41297)">
			<rect x="2" y="2" width="36" height="36" rx="18" fill="#099C73"/>
			<path d="M18.0435 16.5564V25.2825C18.0435 27.4738 17.2609 29.3912 15.7348 30.9564C14.1696 32.5216 12.2913 33.3042 10.0217 33.3042C7.83043 33.3042 5.95217 32.5216 4.38696 30.9173C2.78261 29.352 2 27.4738 2 25.2825V16.5564H6.34348V25.2825C6.34348 26.2607 6.69565 27.1216 7.4 27.8651C8.10435 28.6086 8.96522 28.9607 10.0217 28.9607C11.0391 28.9607 11.9391 28.6086 12.6435 27.8651C13.3478 27.1216 13.7 26.2999 13.7 25.2825V16.5564H18.0435Z" fill="white"/>
			<path d="M31.3871 22.1131C32.7175 21.487 33.8132 20.5087 34.6349 19.2565C35.4567 17.9652 35.848 16.5565 35.848 15.0305C35.848 12.8391 35.0654 10.9609 33.5784 9.4348C32.0132 7.86958 30.1741 7.08698 28.0219 7.08698H20.9001V31.5044C20.9001 31.5044 22.3088 29.8218 23.0523 28.4913C23.561 27.6305 24.2262 26.8087 25.2436 26.4174C25.2045 26.3 25.2045 26.1044 25.2828 26.0261H25.0871C25.0871 26.0261 25.361 22.8565 25.4001 21.8391C25.4393 20.8218 25.4001 18.5522 25.4001 18.5522L24.148 18.4739C24.148 18.4739 24.6175 18.0044 24.5001 17.3391C24.148 16.6739 23.8741 16.1261 23.8741 16.1261C23.8741 16.1261 23.561 15.5 24.0306 14.7174C24.5001 13.9348 24.9306 13.0739 24.9306 13.0739C24.9306 13.0739 25.1654 12.8 25.7523 12.6435C26.3001 12.5261 26.6523 12.2131 26.6915 12.0957C26.7306 12.0174 26.6132 11.7435 26.4567 11.6261C26.3001 11.5087 26.1045 10.8044 26.4958 10.4913C26.8871 10.1391 27.8262 10.2957 27.9045 10.7261C27.9436 11.0391 27.9045 11.1957 27.9045 11.1957H27.9828C27.9828 11.1957 28.0219 11.6652 27.9828 11.7826C27.9436 11.8609 27.8654 11.9 27.8654 11.9783C27.8654 12.0957 27.8262 12.3305 27.9828 12.3305C28.1784 12.3305 28.4915 12.3696 28.4915 12.3696C28.4915 12.3696 28.8828 12.3696 29.0001 12.7218C29.1175 13.0739 29.6654 14.7174 29.7045 14.7957C29.7828 14.9131 29.9002 16.0087 29.7045 16.5957C29.7045 16.8305 29.6654 17.0261 29.5871 17.1435C29.5871 17.1435 29.6262 17.4957 29.3523 17.5348C29.2349 17.6131 28.961 17.9652 28.961 18.1218C28.961 18.2783 29.1958 18.2391 28.961 18.3565C28.7262 18.4739 28.6088 18.3174 28.6088 18.7087C28.6088 19.1 28.1001 21.5261 28.0219 23.0913C27.9436 24.6565 27.9045 25.9478 27.9045 25.9478C27.9045 25.9478 28.3349 26.2218 28.6088 26.5348C29.9001 27.1218 30.9958 28.2565 32.1306 29.9391C32.9523 31.1131 33.461 31.8565 33.7741 32.287H36.0436H38.0393L31.3871 22.1131Z" fill="white"/>
			<path d="M26.8089 25.6738C26.9263 25.1651 26.9655 20.7825 26.9655 20.4694C26.9655 20.1564 26.8481 19.9216 26.8089 20.4303C26.7698 20.939 26.0655 25.8303 26.1046 25.9085C26.1437 25.9477 26.3003 26.0259 26.4568 26.1433C26.5742 26.1433 26.6524 26.1433 26.7698 26.1433C26.7307 26.0651 26.7698 25.9085 26.8089 25.6738Z" fill="white"/>
			<path d="M25.1654 14.9911C25.0871 14.7955 24.6958 15.4216 24.6176 15.7346C24.8915 16.0085 24.9697 16.5563 25.048 16.4781C25.0089 16.165 25.1654 16.165 25.2828 15.7737C25.361 15.3824 25.1654 14.9911 25.1654 14.9911Z" fill="white"/>
			<path d="M29.1566 15.6176C29.1957 15.5002 29.0392 15.2655 28.9218 15.1481C28.9218 15.3046 28.8436 15.422 28.8436 15.422C28.8044 15.5394 28.8044 16.1655 28.8436 16.4394C28.8827 16.7133 29.0001 16.635 29.0783 16.9872C29.0783 16.6742 29.1175 15.735 29.1566 15.6176Z" fill="white"/>
		</g>
		<defs>
			<clipPath id="clip0_590_41297">
				<rect x="2" y="2" width="36" height="36" rx="18" fill="white"/>
			</clipPath>
		</defs>
	</svg>

}