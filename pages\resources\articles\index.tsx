import SupportLayout from "../../../component/SupportLayout";
import {HelpCenterInterface} from "../../../interfaces/HomePageInterfaces";
import {NextPage} from "next";
import ArticlesTheme from "../../../component/Articles/ArticlesTheme";
import React, {memo} from "react";

interface Data {
	bannerData: HelpCenterInterface,
	articlesPage: any
}

const Articles: NextPage<Data> = ({bannerData, articlesPage}) => {
	return (
		<SupportLayout bannerData={bannerData.Banner}>
			{
				articlesPage.map((item: any, index: number) => {
					return  <ArticlesTheme item={item} key={index} link titleFont={24} quantity={0}/>
				})
			}

		</SupportLayout>
	);
};
	


export default Articles;

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
	const articlesPage = await fetch("https://cms-dev.urecruits.com/articles").then(res => res.json());

	return {
		props: {
			articlesPage: articlesPage,
			bannerData: await helpCenterPageRes.json(),
		},
	};
}

