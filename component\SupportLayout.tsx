import { useState } from "react";
import Layout from "./Layout";

import styles from "../styles/HelpCenter.module.scss";
import globalStyle from "../styles/Global.module.scss";

import searchIcon from "../public/images/icon/search_ic_white.svg";
import Link from "next/link";
import { useRouter } from "next/router";

interface ISupportLayout {
  children: any;
  bannerData: {
    description: string;
    headline: string;
    tagline: string;
  };
}

const SupportLayout = ({ children, bannerData }: ISupportLayout) => {
  const router = useRouter();
  const [searchState, setSearchState] = useState("");

  const submitSearchHandler = (e: any) => {
    e.preventDefault();
  };

  return (
    <Layout>
      <section className={styles.banner}>
        <div className={styles.banner__inner}>
          <span className={`${styles.banner__tagline} ${globalStyle.tagline}`}>{bannerData.tagline}</span>
          <h1 className={styles.banner__headline}>{bannerData.headline}</h1>
          <p className={styles.banner__text}>{bannerData.description}</p>
          <label className={styles.banner__label} htmlFor="search">
            <input className={styles.banner__input} placeholder="Search..." id="search" value={searchState} onChange={(e) => setSearchState(e.target.value)} />
            <button className={styles.banner__button} onClick={(e) => submitSearchHandler(e)}>
              <img src={searchIcon.src} alt="search icon" className={styles.banner__button__icon} />
            </button>
          </label>
        </div>
      </section>
      <section className={styles.faq}>
        <div className={styles.faq__inner}>
          <div className={styles.faq__left}>
            <Link href={"/blog"}>
              <a className={`${styles.faq__left__item} ${router.pathname.includes("/blog") ? styles.active : null}`}>Blog</a>
            </Link>
            <Link href={"/articles"}>
              <a className={`${styles.faq__left__item} ${router.pathname.includes("/articles") ? styles.active : null}`}>Articles</a>
            </Link>
            <Link href={"/tutorials"}>
              <a className={`${styles.faq__left__item} ${router.pathname === "/tutorials" ? styles.active : null}`}>Tutorials</a>
            </Link>
            <Link href={"/faq"}>
              <a className={`${styles.faq__left__item} ${router.pathname === "/faq" ? styles.active : null}`}>FAQ & Troubleshooting</a>
            </Link>
            <Link href={"/documentation"}>
              <a className={`${styles.faq__left__item} ${router.pathname === "/documentation" ? styles.active : null}`}>Documentation</a>
            </Link>
            <Link href={"/giving-back-to-communities"}>
              <a className={`${styles.faq__left__item} ${router.pathname.includes("/giving-back-to-communities") ? styles.active : null}`}>
                Giving Back to Communities
              </a>
            </Link>
            <Link href={"https://resources.urecruits.com"}>
              <a className={`${styles.faq__left__item} ${router.pathname === "/resources" ? styles.active : null}`}>Resources</a>
            </Link>
            <Link href={"/disclosure"}>
              <a className={`${styles.faq__left__item} ${router.pathname === "/disclosure" ? styles.active : null}`}>Disclosure</a>
            </Link>
          </div>
          <div className={styles.faq__right}>{children}</div>
        </div>
      </section>
    </Layout>
  );
};

export default SupportLayout;
