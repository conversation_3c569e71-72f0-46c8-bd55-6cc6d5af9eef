@import "mixins";
@import "config";

.about {
  &__inner {
    @include container;
    display: flex;
    justify-content: space-between;
    position: relative;
    @include media(lg) {
      padding-bottom: 20px;
    }
    @include media(md) {
      padding-bottom: unset;
      flex-direction: column;
      position: unset;
    }
  }

  &__right {
    width: calc(45% - 26px);
    @include media(md) {
      width: 100%;
      margin-top: 42px;
    }
  }

  &__left {
    width: 55%;
    @include media(md) {
      width: 100%;
    }
  }

  &__headline {
    margin-bottom: 32px;
    @include media(md) {
      margin-bottom: 16px;
    }
  }

  &__text {
    padding-bottom: 126px;
    @include media(md) {
      padding-bottom: 24px;
    }
  }

  &__information {
    padding: 27px 32px;
    background: $white;
    box-shadow: $cardShadow;
    border-radius: 12px 0 0 12px;
    position: absolute;
    max-width: 930px;
    width: 100%;
    right: 0;
    bottom: 0;
    transform: translateY(-20px);
    display: flex;
    justify-content: center;
    @include media(xl) {
      transform: unset;
    }

    @include media(md) {
      position: unset;
    }
    @include media(xs) {
      padding: 20px 32px 20px 16px;
      width: calc(100% + 32px);
      transform: translateX(0px);
    }

    &__inner {
      max-width: 612px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      @include media(sm) {
        justify-content: space-evenly;
      }
      @include media(xs){
        justify-content: space-between;
      }
    }

    &__item {
      display: flex;
      flex-direction: column;
      padding-right: 12px;
      @include media(xs){
        text-align: center;
      }

      &:last-child {
        padding-right: 0;
      }
    }

    &__value {
      font-size: 32px;
      color: $mainGreen;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 16px;
      @include media(sm) {
        font-size: 20px;
      }
    }

    &__name {
      @include media(sm) {
        font-size: 14px;
      }
    }
  }

}