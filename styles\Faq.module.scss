@import "config";
@import "mixins";

.faq {
  overflow: hidden;
  margin-top: 70px;
  @include media(xs){
    margin-top: 50px;
  }

  &__inner {
    @include container;
    position: relative;
    @include media(xs){
      padding: 0;
    }

    &:before {
      content: "";
      position: absolute;
      width: 475px;
      height: 429px;
      background: url("../public/images/faq/person_right.svg") no-repeat;
      background-size: contain;
      z-index: -1;
      right: 0;
      top: 0;
      transform: translate(264px, 18px);
      @include media(md) {
        display: none;
      }
    }

    &:after {
      content: "";
      position: absolute;
      width: 415px;
      height: 444px;
      background: url("../public/images/faq/person_left.svg") no-repeat;
      background-size: contain;
      z-index: -1;
      left: 0;
      top: 0;
      transform: translate(-266px, 100px);
      @include media(md) {
        display: none;
      }
    }
  }

  &__tagline {
    display: block;
    text-align: center;
  }

  &__headline {
    width: fit-content;
    margin: 0 auto 52px auto;
    text-align: center;
    position: relative;
    @include media(xs){
      padding: 0 32px;
      margin: 0 auto 40px auto;
    }

    &:after {
      content: "";
      position: absolute;
      background: url("../public/images/faq/arrow.svg") no-repeat;
      background-size: contain;
      width: 50px;
      height: 119px;
      left: 0;
      transform: translate(-175px, 10px);
      @include media(md) {
        display: none;
      }
      @include media(xs){
        display: block;
        width: 34px;
        height: 41px;
        transform: translate(49px, -146px);
      }
    }
  }

  &__container {
    margin: 0 auto;
    max-width: 640px;
    width: 100%;
  }

  &__list {
    width: 100%;
  }

  &__item {
    padding: 0 20px;
    margin-top: 48px;
    position: relative;
    transition: .5s ease-in, .5s ease-in-out;
    cursor: pointer;
    @include media(xs) {
      margin-top: 40px;
      padding: 0 32px;
    }


    &:after {
      content: '';
      width: 100%;
      position: absolute;
      border-top: 1px solid $grayTone2;
      top: 0;
      left: 0;
      transform: translateY(-24px);
      @include media(xs) {
        transform: translateY(-20px);
      }
    }

    &:first-child {
      margin-top: 0;

      &:after {
        display: none;
      }
    }

    &__head {
      display: flex;
      justify-content: space-between;
        align-items: flex-start;
    }

    &__name {
      font-weight: 800;
      font-size: 20px;
      color: $grayTone7;
      width: calc(100% - 40px);
      transition: .7s ease-in, .7s ease-in-out;
      @include media(xs){
        font-size: 16px;
      }

    }

    &__close {
      cursor: pointer;
      min-width: 22px;
      width: 22px;
      height: 30px;
      @include media(xs){
        height: 26px;
      }
    }

    &__detail {
      margin-top: 12px;
      font-size: 16px;
      color: $grayTone7;
      cursor: default;
    }
  }

  &__item.active {
    background: $milkWhite;
    border-radius: 8px;
    padding: 24px 20px;
    @include media(xs) {
      padding: 12px 32px;
    }

    .faq__item__name {
      color: $mainGreen;
    }

    .faq__item__close {
      transform: rotate(180deg);

      svg {
        path {
          stroke: $mainGreen;
        }
      }
    }
  }

  &__all {
    display: inline-block;
    margin-top: 52px;
    margin-left: 20px;
    @include media(xs){
      margin-left: 32px;
    }
  }
}