import { NextComponentType } from 'next'
import styles from '../../styles/Services.module.scss'
import globalStyle from '../../styles/Global.module.scss'
import {ServicesPageInterface} from "../../interfaces/HomePageInterfaces";

type Props = {
  props: ServicesPageInterface
}

const ServicesSteps = ({props}: Props) => {
  const step=props.step
  return (
    <section className={styles.services}>
      <div className={styles.services__inner}>
        <div className={styles.services__block}>
          <span className={`${styles.services__tagline} ${globalStyle.tagline}`}>{step[0].tagline}</span>
          <div className={styles.services__block__item}>
            <div className={styles.services__block__left}>
              <h2 className={styles.services__headline}>{step[0].headline}</h2>
            </div>
            <div className={styles.services__block__right}>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[0].sub_step[0].image.url}
                      alt={'uRecruits'}
                    />
                  </div>
                  <h3 className={styles.services__information__name}>{step[0].sub_step[0].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[0].sub_step[0].description}
                </p>
              </div>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[0].sub_step[1].image.url}
                      alt={'uRecruits'}
                    />
                  </div>
                  <h3 className={styles.services__information__name}>{step[0].sub_step[1].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[0].sub_step[1].description}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.services__block}>
          <span className={`${styles.services__tagline} ${globalStyle.tagline}`}>{step[1].tagline}</span>
          <div className={styles.services__block__item}>
            <div className={styles.services__block__left}>
              <h2 className={styles.services__headline}>{step[1].headline}</h2>
            </div>
            <div className={styles.services__block__right}>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[1].sub_step[0].image.url}
                      alt={'uRecruits'}
                    />
                  </div>
                  <h3 className={styles.services__information__name}>{step[1].sub_step[0].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[1].sub_step[0].description}
                </p>
              </div>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[1].sub_step[1].image.url}
                      alt={'uRecruits'}/>
                  </div>
                  <h3 className={styles.services__information__name}>{step[1].sub_step[1].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[1].sub_step[1].description}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.services__block}>
          <span className={`${styles.services__tagline} ${globalStyle.tagline}`}>{step[2].tagline}</span>
          <div className={styles.services__block__item}>
            <div className={styles.services__block__left}>
              <h2 className={styles.services__headline}>
                {step[2].headline}
              </h2>
            </div>
            <div className={styles.services__block__right}>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[2].sub_step[0].image.url}
                      alt={'uRecruits'}
                    />
                  </div>
                  <h3 className={styles.services__information__name}>{step[2].sub_step[0].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[2].sub_step[0].description}
                </p>
              </div>
              <div className={styles.services__information}>
                <div className={styles.services__information__wrap}>
                  <div className={styles.services__information__image}>
                    <img
                      src={step[2].sub_step[1].image.url}
                      alt={'uRecruits'}
                    />
                  </div>
                  <h3 className={styles.services__information__name}>{step[2].sub_step[1].title}</h3>
                </div>
                <p className={styles.services__information__description}>
                  {step[2].sub_step[1].description}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ServicesSteps