import Link from "next/link";
import {memo} from "react";
import styles from '../../styles/articles-theme.module.scss'
import ArticlesBoxContent from "./ArticlesBoxContent";

export default memo(({item, styleProp, link,titleFont,quantity}: { item: any, styleProp?: any, link?: boolean,titleFont:number,quantity:number }) => {

	return link ?
		<Link href={`/articles/${item.slug}`} passHref>
			<a className={styles.container} style={styleProp}>
				<Block item={item} titleFont={24} quantity={quantity}/>
			</a>
		</Link>
		:
		<div className={styles.container} style={styleProp}>
			<Block item={item} titleFont={titleFont} quantity={quantity}/>
		</div>

})

const Block = memo(({item,titleFont,quantity}: { item: any,titleFont:number,quantity:number }) => {
	return <>
		<img src={item.icon?.url} alt={'icon'} className={styles.container__icon}/>
		<div className={styles.container__inner}>
			<ArticlesBoxContent title={item.Title} desc={item.description} quantity={item.sub_articles?.length||quantity}
			                    updatedAt={item.updatedAt} titleFont={titleFont}/>
		</div>
	</>
})
