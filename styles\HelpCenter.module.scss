@import "config";
@import "mixins";

.banner {
  padding-top: 182px;
  margin-top: -122px;
  background: $milkWhite url("../public/images/help-center/help-center-banner-bg.svg");

  &__inner {
    @include container;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__headline {
    margin-bottom: 28px;
  }

  &__text {
    margin-bottom: 52px;
    font-family: 'Avenir LT Std', sans-serif;
    font-size: 20px;
    line-height: 1.5;
    color: $grayTone5;
  }

  &__label {
    max-width: 870px;
    width: 100%;
    padding: 9px 24px;
    background: $white;
    border: 1px solid $grayTone2;
    box-sizing: border-box;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__input {
    width: calc(100% - 55px);

    &::placeholder {
      color: $grayTone4;
    }
  }

  &__image {
    width: 40px;
    height: 40px;
    padding-left: 10px;
    cursor: pointer;
  }

  &__button{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background: #099c73a6;
    cursor: pointer;
    transition: .3s ease-in, .3s ease-in-out;

    &:hover{
      background: $mainGreen;
    }

    &__icon{
      width: 20px;
      height: 20px;
      object-fit: contain;
    }
  }
}

.faq {

  &__inner {
    @include container;
    display: flex;
    width: 100%;
    align-items: flex-start;
    justify-content: space-between;
    @include media(md) {
flex-direction: column;
    }
  }

  &__left {
    max-width: 240px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-right: 88px;
    position: relative;
    @include media(lg) {
      margin: 0 0 32px 0;
    }

    @include media(md) {
      justify-content: center;
      max-width: 100%;
    }
    &__item.active {
      background: rgba(172, 216, 209, 0.3);
      color: $mainGreen;
      border-radius: 1px 4px 4px 1px;
      @include media(xs) {
        background: unset;
        color: $black;
        border-radius: unset;
      }

      &:hover {
        color: $mainGreen;
        @include media(xs) {
          color: $black;
        }
      }

      &:after {
        content: "";
        position: absolute;
        width: 3px;
        height: 100%;
        background: $mainGreen;
        display: block;
        left: 0;
        bottom: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        transition: .3s ease-in, .3s ease-in-out;
        @include media(xs) {
          border-top-left-radius: unset;
          border-bottom-left-radius: unset;
          width: 100%;
          height: 2px;
        }
      }
    }
    &__item {
      padding: 11px 16px;
      font-weight: 500;
      color: $grayTone4;
      font-size: 16px;
      position: relative;
      width: 100%;
      transition: .3s ease-in, .3s ease-in-out;
      cursor: pointer;
      @include media(xs) {
        width: fit-content;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        color: $mainGreen;
        @include media(xs) {
          color: $black;
        }
      }
    }
  }

  &__right{
    width: calc(100% - 311px);
    @include media(lg){
      width: calc(100% - 263px);
    }
    @include media(md){
      width: 100%;
    }
  }

  &__info{
    width: 100%;
    background: $white;
    border: 1px solid $grayTone2;
    box-sizing: border-box;
    border-radius: 12px;
    padding: 24px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    &__right{
      width: calc(100% - 120px);
    }

    &__title{
      margin-bottom: 17px;
    }

    &__description{
      margin-bottom: 24px;
    }

    &__image{
      width: 60px;
      height: 60px;
    }
  }
  &__author{
    display: flex;
    align-items: center;
    &__image{
      width: 40px;
      height: 40px;
      margin-right: 16px;
    }
    &__block{
      display: flex;
      flex-direction: column;
    }

    &__text{
      font-size: 14px;
      line-height: 1.4;
      font-weight: 800;
      span{
        font-weight: 400;
      }
    }
  }
}