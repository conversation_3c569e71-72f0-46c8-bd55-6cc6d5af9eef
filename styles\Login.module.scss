@import "config";
@import "mixins";

.login {
  @include authSection;

  &__inner {
    @include authContainer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    @include media(md) {
      justify-content: center;
    }
  }

  &__headlin{
    font-weight: 500;
  }

  &__header {
    width: 100%;
  }

  &__left {
    max-width: 420px;
    width: 100%;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
  }

  &__right {
    width: calc(50% + 60px);
    display: flex;
    align-items: center;
    @include media(md) {
      display: none;
    }
  }

  &__alternative {
    padding: 40px 0;
    border-bottom: 1px solid $grayTone3;
    margin-bottom: 38px;
    display: flex;
    justify-content: space-between;
    @include media(xs) {
      padding-top: 31px;
      padding-bottom: 24px;
      margin-bottom: 33px;
    }

    &__button {
      width: 100%;
      max-width: 120px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $grayTone1;
      border: 1px solid $grayTone2;
      box-sizing: border-box;
      border-radius: 4px;
      cursor: pointer;
      @include media(xs) {
        max-width: 105px;
      }
      @include media(xss) {
        max-width: 90px;
      }
    }

    &__icon {
      max-width: 24px;
      width: 100%;
      height: 24px;
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    padding-top: 40px;

    &__item {
      position: relative;
    }

    &__item.error {
      .errorMessage {
        display: block;
      }
    }

    &__password_visibility{
      width: 20px;
      height: 18px;
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(-15px, 38px);
      cursor: pointer;
    }

    .errorMessage {
      display: none;
      font-size: 12px;
      line-height: 1;
      color: $red;
      position: absolute;
      top: 100%;
      left: 0;
      width: 100%;
      transform: translateY(-14px);
    }

    &__input {
      @include input;
      margin-bottom: 25px;
    }

    &__label {
      @include label;
    }

    &__items {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 3px;
      margin-bottom: 34px;
      @include media(xs) {
        margin-bottom: 38px;
      }
    }

    &__save {
      display: flex;
      align-items: center;
    }

    &__checkbox {
      @include customCheckbox;
    }

    &__saveText {
      margin-left: 8px;
      font-style: normal;
      font-weight: normal;
      font-size: 14px;
      line-height: 100%;
      color: $grayTone7;
      cursor: default;

      @include media(xs) {
        font-size: 12px;
      }
    }

    &__link {
      font-style: normal;
      font-weight: normal;
      font-size: 14px;
      line-height: 14px;
      color: $mainGreen;
      transition: .3s ease-in, .3s ease-in-out;
      @include media(xs) {
        font-size: 12px;
      }

      &:hover{
        text-decoration: underline;
      }
    }

    &__buttonWrap {
      display: flex;
      justify-content: flex-end;
      width: 100%;
    }

    &__button {
      font-size: 14px;
    }
  }
}