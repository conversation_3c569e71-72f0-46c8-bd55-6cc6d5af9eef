@import "config";
@import "mixins";

.vision {
  padding-top: 190px;
  padding-bottom: 150px;
  @include media(md) {
    padding: 70px 0;
  }
  @include media(xs) {
    padding: 50px 0;
  }

  &__inner {
    @include container();
    display: flex;
    justify-content: space-evenly;
    @include media(md) {
      flex-direction: column;
    }
  }

  &__item {
    max-width: 452px;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    @include media(md) {
      max-width: 100%;
      margin-bottom: 42px;
    }

    &:last-child {
      @include media(sm) {
        margin-bottom: 0;
      }
    }

    &__image {
      margin-bottom: 20px;
    }

    &__tagline {
      margin-bottom: 12px;
      text-transform: uppercase;
      font-weight: 500;
      color: $greenBlue1;
    }

    &__title {
      color: $black;
      font-size: 26px;
      font-weight: 800;
      text-align: center;
    }
  }
}