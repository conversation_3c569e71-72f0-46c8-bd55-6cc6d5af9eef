@import "config";
@import "mixins";

.happiness {
  &__inner {
    @include container();
  }

  &__tagline {

  }

  &__headline {
    margin-bottom: 12px;
    max-width: 400px;

    span {
      color: $mainGreen;
    }
  }

  &__body {
    min-height: 600px;
    margin-top: 40px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  &__image {
    position: absolute;

    @include media(xs) {
      width: 40%;
      object-fit: contain;
      position: relative;
      margin-bottom: 20px;
    }

    img {
      border-radius: 50%;
    }
  }

  &__people1 {
    width: 19%;
    left: 28%;
    top: 6%;

    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people2 {
    width: 9%;
    top: 0;
    left: 52%;

    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people3 {
    width: 9%;
    top: 52%;
    left: 26%;

    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people4 {
    width: 9%;
    left: 65%;
    top: 71%;

    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people5 {
    width: 9%;
    right: 10%;
    top: 22%;

    @include media(xs) {
      width: 48%;
      top: unset;
      right: unset;
    }
  }

  &__people6 {
    width: 10%;
    opacity: .5;
    top: 7%;
    left: 13%;

    @include media(xs) {
      opacity: 1;
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people7 {
    width: 15%;
    right: 25%;
    top: 20%;
    @include media(xs) {
      width: 48%;
      top: unset;
      right: unset;
    }
  }

  &__people8 {
    width: 14%;
    right: 0;
    top: 44%;
    @include media(xs) {
      width: 48%;
      top: unset;
      right: unset;
    }
  }

  &__people9 {
    width: 21%;
    left: 0;
    top: 32%;
    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people10 {
    width: 10%;
    opacity: .5;
    top: 76%;
    left: 18%;
    @include media(xs) {
      opacity: 1;
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people11 {
    width: 12%;
    left: 47%;
    top: 46%;
    @include media(xs) {
      width: 48%;
      top: unset;
      left: unset;
    }
  }

  &__people12 {
    width: 7%;
    right: 4%;
    top: 3%;
    @include media(xs) {
      width: 48%;
      top: unset;
      right: unset;
    }
  }

  &__vector1 {
    width: 6%;
    left: 39%;
    top: 72%;
    @include media(xs) {
      display: none;
    }
  }

  &__vector2 {
    width: 4%;
    top: 55%;
    right: 21%;
    @include media(xs) {
      display: none;
    }
  }
}