import AuthLayout from "../../component/Auth/AuthLayout";
import styles from "../../styles/CandidateRegistration.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import Image from "next/image";
import { useEffect, useState } from "react";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import figure2 from "../../public/images/registration/figure2.svg";
import figure3 from "../../public/images/registration/figure3.svg";
import figure4 from "../../public/images/registration/figure4.svg";
import Head from "next/head";
import { selectCustomStyle } from "../../public/files/selectCustomStyle";
import { validateEmail } from "../../hook/validateEmail";
import { validatePassword } from "../../hook/validatePassword";
import { validateDomain } from "../../hook/validateDomain";
import Link from "next/link";
import sbsPasswordCheckForErrors from "../../hook/sbsPasswordCheckForErrors";
import { NextPage } from "next";
import axios from "axios";
import AsyncSelect from "react-select/async";
import { selectSearchFuncIndustry } from "../../hook/searchFunctions";
import { selectCustomErrorStyle } from "../../public/files/selectCustomErrorStyle";

import hidePasswordIc from "../../public/images/icon/hide_password_ic.svg";
import displayPasswordIc from "../../public/images/icon/open_password_ic.svg";


const Company: NextPage = () => {
	const [authError, setAuthError] = useState(false);
	const [sbsPasswordValidation, setSbsPasswordValidation] = useState(false);
	const [progress, setProgress] = useState("first");

	const [disableFirstStepButton, setDisableFirstStepButton] = useState(false);
	const [disableSecondStepButton, setDisableSecondStepButton] = useState(false);
	const [disableThirdStepButton, setDisableThirdStepButton] = useState(false);

	//display/hide password
	const [displayNewPassword, setDisplayNewPassword] = useState(false);
	const [displayConfirmPassword, setDisplayConfirmPassword] = useState(false);

	//first step state
	const [companyNameError, setCompanyNameError] = useState(false);
	const [companyEmailError, setCompanyEmailError] = useState(false);
	const [emailExistError, setEmailExistError] = useState(false);
	const [companyPhoneError, setCompanyPhoneError] = useState(false);
	const [companyTypeError, setCompanyTypeError] = useState(false);
	const [companyIndustryError, setCompanyIndustryError] = useState(false);

	const [companyName, setCompanyName] = useState("");
	const [companyEmail, setCompanyEmail] = useState("");
	const [companyPhone, setCompanyPhone] = useState<any>(undefined);
	const [companyType, setCompanyType] = useState("");
	const [companyIndustry, setCompanyIndustry] = useState<any>(null);

	//second step state
	const [contactPersonFirstNameError, setContactPersonFirstNameError] = useState(false);
	const [contactPersonLastNameError, setContactPersonLastNameError] = useState(false);
	const [contactPersonPhoneError, setContactPersonPhoneError] = useState(false);

	const [contactPersonFirstName, setContactPersonFirstName] = useState("");
	const [contactPersonMiddleName, setContactPersonMiddleName] = useState("");
	const [contactPersonLastName, setContactPersonLastName] = useState("");
	const [contactPersonPhone, setContactPersonPhone] = useState<any>(undefined);
	//third step state
	const [domainError, setDomainError] = useState(false);
	const [domainExistError, setDomainExistError] = useState('');
	const [passwordError, setPasswordError] = useState(false);
	const [confirmPasswordError, setConfirmPasswordError] = useState(false);
	const [iAgreeError, setIAgreeError] = useState(true);

	const [domain, setDomain] = useState("");
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [iAgree, setIAgree] = useState(false);

	//validate password for step by step
	const [minLength, setMinLength] = useState(false);
	const [oneNumber, setOneNumber] = useState(false);
	const [azSmallSymbol, setAzSmallSymbol] = useState(false);
	const [azBigSymbol, setAzBigSymbol] = useState(false);
	const [specialCharacters, setSpecialCharacters] = useState(false);

	useEffect(() => {
		sbsPasswordCheckForErrors(password, setMinLength, setOneNumber, setAzSmallSymbol, setAzBigSymbol, setSpecialCharacters);
	}, [password]);

	const validateFirstStep = (): boolean => {
		return companyName.length !== 0 && companyIndustry && companyType.length !== 0 && companyPhone !== undefined && isValidPhoneNumber(companyPhone) && !emailExistError && validateEmail(companyEmail);
	};

	const validateSecondStep = (): boolean => {
		return contactPersonFirstName.length !== 0 && contactPersonLastName.length !== 0
			&& contactPersonPhone !== undefined && isValidPhoneNumber(companyPhone) && validateFirstStep();
	};

	const validateThirdStep = (): boolean => {
		return domain.length !== 0 && password.length > 0 && confirmPassword.length > 0
			&& password === confirmPassword && iAgree && !validateDomain(domain)?.message && validateFirstStep() && validateSecondStep();
	};

	const moveToSecondStep = async (e: any) => {
		e.preventDefault();
		companyName.length === 0 && (setCompanyNameError(true));
		!companyIndustry && (setCompanyIndustryError(true));
		!companyType && (setCompanyTypeError(true));
		validateEmail(companyEmail) ? setCompanyEmailError(false) : setCompanyEmailError(true);
		companyPhone !== undefined && isValidPhoneNumber(companyPhone) ? setCompanyPhoneError(false) : setCompanyPhoneError(true);

		if (validateFirstStep()) {
			axios.get(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/user/${companyEmail}`).then(() => {
				setProgress("second");
				setEmailExistError(false);
			}).catch(() => {
				setEmailExistError(true);
			});
		}
	};

	const moveToThirdStep = (e: any) => {
		e.preventDefault();
		contactPersonFirstName.length === 0 && (setContactPersonFirstNameError(true));
		contactPersonLastName.length === 0 && (setContactPersonLastNameError(true));
		contactPersonPhone !== undefined && isValidPhoneNumber(contactPersonPhone)
			? setContactPersonPhoneError(false)
			: setContactPersonPhoneError(true);

		if (validateSecondStep()) setProgress("third");
	};

	const onSubmitFormHandler = async (e: any) => {
		e.preventDefault();
		const { message } = validateDomain(domain);
		if (message) {
			setDomainError(true);
			setDomainExistError(message);
		} else {
			setDomainError(false);
			setDomainExistError('');
		}
		validatePassword(password) ? setPasswordError(false) : setPasswordError(true);
		password !== confirmPassword ? setConfirmPasswordError(true) : setConfirmPasswordError(false);
		if (!iAgree) setIAgreeError(false);

		if (validateThirdStep()) {
			const formDataCompany = {
				name: companyName,
				email: companyEmail,
				password: password,
				companyPhone: companyPhone,
				tenantId: domain,
				company_type: companyType,
				industryId: companyIndustry?.id,
				phone: contactPersonPhone,
				firstname: contactPersonFirstName,
				middlename: contactPersonMiddleName,
				lastname: contactPersonLastName,
			};
			await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/company`, {
				headers: {
					"content-type": "application/json",
				},
				method: "POST",
				body: JSON.stringify(formDataCompany),
			}).then(function (res) {
				return res.json();
			}).then(async function (body) {
				if (body.statusCode) {
					setAuthError(body.message);
				} else {
					location.replace("/api/auth/login");
				}
			});
		}
	};

	const checkDomain = async () => {
		if (!domainError) {
			await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/company/check/${domain}`).then(function (res) {
				if (res.status == 200) {
					setDomainExistError('');
				} else {
					setDomainExistError('Domain already exist. Please choose another one');
				}
			});
		}
	};


	const checkUserEmail = () => {
		if (!companyEmailError) {
			axios.get(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/user/${companyEmail}`).then(() => {
				setEmailExistError(false);
			}).catch(() => {
				setEmailExistError(true);
			});
		}
	};

	useEffect(() => {
		if (validateFirstStep()) {
			setDisableFirstStepButton(true);
		} else {
			setDisableFirstStepButton(false);
		}
	}, [companyName, companyEmail, companyPhone, companyType, companyIndustry, emailExistError]);

	useEffect(() => {
		if (validateSecondStep()) {
			setDisableSecondStepButton(true);
		} else {
			setDisableSecondStepButton(false);
		}
	}, [contactPersonFirstName, contactPersonLastName, contactPersonPhone]);

	useEffect(() => {
		if (validateThirdStep()) {
			setDisableThirdStepButton(true);
		} else {
			setDisableThirdStepButton(false);
		}
	}, [password, confirmPassword, domain, iAgree]);

	return (
		<AuthLayout>
			<Head>
				<title>Registration Company | uRecruits</title>
			</Head>
			<section className={`${styles.registration} ${styles.company}`}>
				<div className={styles.registration__inner}>
					<div className={styles.registration__left}>
						<div className={`${styles.registration__left__head} ${styles.withoutList}`}>
							<span className={globalStyle.tagline}>
								Welcome to uRecruits
							</span>
							<h2 className={styles.registration__headline}>
								We’re ready to set up your free trial
							</h2>
						</div>
						<div className={`${styles.registration__progress} ${progress === "first" ? styles.first : ""} 
            ${progress === "second" ? styles.second : ""} ${progress === "third" ? styles.third : ""}`}>
							<div className={styles.registration__progress__item}>
								<div className={styles.registration__progress__dots}></div>
								<div className={styles.registration__progress__name}>
									Company Info
								</div>
							</div>
							<div className={styles.registration__progress__item}>
								<div className={styles.registration__progress__dots}></div>
								<div className={styles.registration__progress__name}>
									Personal Info
								</div>
							</div>
							<div className={styles.registration__progress__item}>
								<div className={styles.registration__progress__dots}></div>
								<div className={styles.registration__progress__name}>
									Domain Info
								</div>
							</div>
						</div>
						<form
							className={styles.registration__form}
							onSubmit={(e) => onSubmitFormHandler(e)}
						>
							{
								progress === "first" && (
									<div className={styles.registration__form__step}>
										<div className={`${styles.registration__form__item} ${companyNameError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Company Name<span> *</span>
											</label>
											<input
												type="text"
												placeholder="Enter company name"
												className={styles.registration__form__input}
												value={companyName}
												onChange={(e) => {
													setCompanyName(e.target.value);
													e.target.value.length === 0 ? setCompanyNameError(true) : setCompanyNameError(false);
												}}
											/>
											<p className={styles.errorMessage}>
												This is requred field
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${companyEmailError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Company Email<span> *</span>
											</label>
											<input
												type="email"
												placeholder="Enter company email"
												className={styles.registration__form__input}
												value={companyEmail}
												onChange={(e) => {
													setCompanyEmail(e.target.value);
													validateEmail(e.target.value) ? setCompanyEmailError(false) : setCompanyEmailError(true);

												}}
												onBlur={() => {
													checkUserEmail();
												}}
											/>
											<p>
												<span className={styles.existError}>
													{companyEmailError && "Please enter a valid Email address."}{emailExistError && "This user already exist."}
												</span>
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${companyPhoneError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Phone<span> *</span>
											</label>
											{/*@ts-ignore*/}
											<PhoneInput
												international
												defaultCountry="US"
												value={companyPhone}
												onChange={(value: any) => {
													if (value) {
														setCompanyPhone(value);
														isValidPhoneNumber(value) ? setCompanyPhoneError(false) : setCompanyPhoneError(true);
													} else {
														setCompanyPhoneError(true);
													}
												}}
											/>
											<p className={styles.errorMessage}>
												Please enter a valid Phone number
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${companyTypeError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Company Type<span> *</span>
											</label>
											<div className={styles.registration__form__group}>
												<div className={styles.registration__form__radio}>
													<input
														type="radio"
														id="corporate"
														name="companyType"
														value="Corporate"
														checked={companyType === "Corporate"}
														onChange={(e) => {
															setCompanyType(e.target.value);
															e.target.value.length === 0 ? setCompanyTypeError(true) : setCompanyTypeError(false);
														}}
													/>
													<label htmlFor="corporate">Corporate</label>
												</div>
												<div className={styles.registration__form__radio}>
													<input
														type="radio"
														id="consultancy"
														name="companyType"
														value="Consultancy"
														checked={companyType === "Consultancy"}
														onChange={(e) => {
															setCompanyType(e.target.value);
															e.target.value.length === 0 ? setCompanyTypeError(true) : setCompanyTypeError(false);
														}}
													/>
													<label htmlFor="consultancy">Consultancy</label>
												</div>
												<div className={styles.registration__form__radio}>
													<input
														type="radio"
														id="Other"
														name="companyType"
														value="Other"
														checked={companyType === "Other"}
														onChange={(e) => {
															setCompanyType(e.target.value);
															e.target.value.length === 0 ? setCompanyTypeError(true) : setCompanyTypeError(false);
														}}
													/>
													<label htmlFor="Other">Other</label>
												</div>
											</div>
											<p className={styles.errorMessage}>
												This is requred field
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${companyIndustryError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Company Industry<span> *</span>
											</label>
											<AsyncSelect
												cacheOptions
												loadOptions={(inputValue: string) => inputValue.length > 0 ? selectSearchFuncIndustry(inputValue) : selectSearchFuncIndustry("")}
												closeMenuOnSelect={true}
												hideSelectedOptions={false}
												defaultOptions
												value={companyIndustry}
												onChange={(option: any) => {
													setCompanyIndustry(option);
													setCompanyIndustryError(false);
												}}
												styles={!companyIndustryError ? selectCustomStyle : selectCustomErrorStyle}
												placeholder={"Choose company industry"}
												id="industry-select"
												instanceId="industry-select"
											/>
											<p className={styles.errorMessage}>
												This is requred field
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${styles.rightPosition} ${styles.buttons}`}>
											<div className={styles.registration__form__container}>
												<button
													className={`${styles.registration__form__submit} ${globalStyle.filledButton} 
                          ${disableFirstStepButton ? "" : globalStyle.disableFilledButton} `}
													onClick={(e) => moveToSecondStep(e)}
												>
													Next
												</button>
											</div>
										</div>
									</div>
								)
							}
							{
								progress === "second" && (
									<div className={styles.registration__form__step}>
										<div className={`${styles.registration__form__item} ${styles.short} ${contactPersonFirstNameError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Contact Person First Name<span> *</span>
											</label>
											<input
												type="text"
												placeholder="Enter first name"
												className={styles.registration__form__input}
												value={contactPersonFirstName}
												onChange={(e) => {
													setContactPersonFirstName(e.target.value);
													e.target.value.length === 0 ? setContactPersonFirstNameError(true) : setContactPersonFirstNameError(false);
												}}
											/>
											<p className={styles.errorMessage}>
												This is requred field
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${styles.short} `}>
											<label className={styles.registration__form__label}>
												Middle Name
											</label>
											<input
												type="text"
												placeholder="Enter middle name"
												className={styles.registration__form__input}
												value={contactPersonMiddleName}
												onChange={(e) => {
													setContactPersonMiddleName(e.target.value);
												}}
											/>

										</div>
										<div className={`${styles.registration__form__item} ${contactPersonLastNameError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Contact Person Last Name<span> *</span>
											</label>
											<input
												type="text"
												placeholder="Enter last name"
												className={styles.registration__form__input}
												value={contactPersonLastName}
												onChange={(e) => {
													setContactPersonLastName(e.target.value);
													e.target.value.length === 0 ? setContactPersonLastNameError(true) : setContactPersonLastNameError(false);
												}}
											/>
											<p className={styles.errorMessage}>
												This is requred field
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${contactPersonPhoneError ? styles.error : ""}`}>
											<label className={styles.registration__form__label}>
												Contact Person Phone<span> *</span>
											</label>
											{/*@ts-ignore*/}
											<PhoneInput
												international
												defaultCountry="US"
												value={contactPersonPhone}
												onChange={(value: any) => {
													if (value) {
														setContactPersonPhone(value);
														isValidPhoneNumber(value) ? setContactPersonPhoneError(false) : setContactPersonPhoneError(true);
													} else {
														setContactPersonPhoneError(true);
													}
												}}
											/>
											<p className={styles.errorMessage}>
												Please enter a valid Phone number
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${styles.rightPosition} ${styles.buttons}`}>
											<div
												className={`${styles.registration__form__buttonBack} ${globalStyle.emptyButton}`}
												onClick={() => setProgress("first")}
											>
												Back
											</div>
											<div className={styles.registration__form__container}>
												<button
													className={`${styles.registration__form__submit} ${globalStyle.filledButton} ${disableSecondStepButton
														? ""
														: globalStyle.disableFilledButton} `}
													onClick={(e) => moveToThirdStep(e)}
												>
													Next
												</button>
											</div>
										</div>
									</div>
								)
							}
							{
								progress === "third" && (
									<div className={styles.registration__form__step}>
										<div className={`${styles.registration__form__item} ${styles.textItem}`}>
											<p className={styles.registration__form__information}>
												Here is your uRecruits domain. Everyone on your account will need to use this unique link to log in to and use
												uRecruits.
											</p>
											<p className={styles.registration__form__name}>
												.urecruits.com
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${domainError ? styles.error : ""}`}>
											<div className={styles.registration__form__container}>
												<label className={styles.registration__form__label}>
													uRecruits Domain<span> *</span>
												</label>
												<p className={`${styles.registration__form__domain} ${domainExistError ? styles.error : ""}`}><span>{domain}</span>.urecruits.com
												</p>
											</div>
											<input
												type="text"
												placeholder="Enter domain for company"
												className={styles.registration__form__input}
												value={domain}
												onChange={(e) => {
													setDomain(e.target.value);
													const { message } = validateDomain(e.target.value);
													if (message) {
														setDomainError(true);
														setDomainExistError(message);
													} else {
														setDomainError(false);
														setDomainExistError('');
													}
												}}
												onBlur={() => {
													checkDomain();
												}}
											/>
											{!!domainExistError && <p className={styles.existError}>{domainExistError}</p>}
										</div>
										<div className={`${styles.registration__form__item} ${styles.registration__form__item__password} ${passwordError ? styles.passwordError : ""}
                     ${confirmPasswordError ? styles.confirmPasswordError : ""}`}>
											<div className={styles.registration__form__container}>
												<label className={styles.registration__form__label}>
													Password<span> *</span>
												</label>
												<div className={styles.registration__validate}>
													<span
														className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""} ${minLength ? styles.active : ""}`}>
														8 min,
													</span>
													<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${oneNumber ? styles.active : ""}`}>
														1 num,
													</span>
													<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${azSmallSymbol ? styles.active : ""}`}>
														a-z,
													</span>
													<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${azBigSymbol ? styles.active : ""}`}>
														A-Z,
													</span>
													<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${specialCharacters ? styles.active : ""}`}>
														special characters
													</span>
												</div>
											</div>
											<div className={styles.passwordInner}>
												<input
													type={displayNewPassword ? "text" : "password"}
													placeholder="Enter password"
													className={`${styles.registration__form__input} ${styles.password}`}
													value={password}
													onChange={(e) => {
														setPassword(e.target.value);
														validatePassword(e.target.value) ? setPasswordError(false) : setPasswordError(true);
														setSbsPasswordValidation(true);
													}}
												/>
												{
													!displayNewPassword ? (
														<img
															src={hidePasswordIc.src}
															alt="icon"
															className={styles.passwordType}
															onClick={() => setDisplayNewPassword(true)}
														/>
													) : (
														<img
															src={displayPasswordIc.src}
															alt="icon"
															className={styles.passwordType}
															onClick={() => setDisplayNewPassword(false)}
														/>
													)
												}
											</div>
											<div className={styles.passwordInner}>
												<input
													type={displayConfirmPassword ? "text" : "password"}
													placeholder="Confirm password"
													className={`${styles.registration__form__input} ${styles.confirm} ${styles.confirmPassword}`}
													value={confirmPassword}
													onChange={(e) => {
														setConfirmPassword(e.target.value);
														password !== e.target.value ? setConfirmPasswordError(true) : setConfirmPasswordError(false);
													}}
												/>
												{
													!displayConfirmPassword ? (
														<img
															src={hidePasswordIc.src}
															alt="icon"
															className={styles.passwordType}
															onClick={() => setDisplayConfirmPassword(true)}
														/>
													) : (
														<img
															src={displayPasswordIc.src}
															alt="icon"
															className={styles.passwordType}
															onClick={() => setDisplayConfirmPassword(false)}
														/>
													)
												}
											</div>
											<p className={styles.errorMessage}>
												{
													confirmPasswordError && ("Fields must be match.")
												}
												{
													passwordError && (" Password does not match validation.")
												}
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${styles.iAgree} ${!iAgreeError ? styles.error : ""}`}>
											<div className={`${styles.registration__form__container} ${styles.rights}`}>
												<div className={styles.registration__form__checkbox}>
													<input
														type="checkbox"
														id="confirmCheckbox"
														checked={iAgree}
														onChange={() => {
															setIAgree(!iAgree);
															setIAgreeError(!iAgree);
														}}
													/>
													<label htmlFor="confirmCheckbox"><span /></label>
												</div>
												<p className={styles.registration__form__text}
													onClick={() => {
														setIAgree(!iAgree);
														setIAgreeError(!iAgree);
													}}
												>
													I agree with the
													<Link href={"/terms-of-service"}>
														<a className={styles.registration__form__link}> Terms of Service </a>
													</Link>
													and
													<Link href={"/privacy-policy"}>
														<a className={styles.registration__form__link}> Privacy Policy </a>
													</Link>
												</p>
											</div>
											<p className={styles.errorMessage}>
												You must accept the privacy terms
											</p>
										</div>
										<div className={`${styles.registration__form__item} ${styles.rightPosition} ${styles.buttons}`}>
											<div
												className={`${styles.registration__form__buttonBack} ${globalStyle.emptyButton}`}
												onClick={() => setProgress("second")}
											>
												Back
											</div>
											<div className={styles.registration__form__container}>
												<button
													className={`${styles.registration__form__submit} ${globalStyle.filledButton} ${disableThirdStepButton
														? ""
														: globalStyle.disableFilledButton} `}
													type="submit"
												>
													Create account
												</button>
												{authError && <p className={styles.authError}>{authError}</p>}
											</div>
										</div>
									</div>
								)
							}
						</form>
					</div>
					<div className={styles.registration__right}>
						<Image src={progress === "first" ? figure3 : progress === "second" ? figure2 : progress === "third" ? figure4 : null} />
					</div>
				</div>
			</section>
		</AuthLayout>
	);
};

Company.getInitialProps = async () => {
	const response = await fetch("https://cms-dev.urecruits.com/industries-and-positions");
	const data = await response.json();
	return { data };
};

export default Company;