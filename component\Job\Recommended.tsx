import image from '../../public/images/tempUser.png'
import paperclip from '../../public/images/icon/paperclip.svg'
import styles from '../../styles/Recommended.module.scss'
import Link from 'next/link'

const Recommended = () => {

  return (
    <div className={styles.recommended}>
      <p className={styles.recommended__headline}>
        Also recommended these job
      </p>
      <div className={styles.recommended__list}>
        <div className={styles.recommended__item}>
          <div className={styles.recommended__top}>
            <Link href="/">
              <a className={styles.recommended__top__link}>
                Candidate matched: 98%
              </a>
            </Link>
          </div>
          <div className={styles.recommended__body}>
            <img src={image.src} alt="" className={styles.recommended__body__image}/>
            <div className={styles.recommended__body__inner}>
              <p className={styles.recommended__body__title}>
                Alex <PERSON>
              </p>
              <ul className={styles.recommended__info}>
                <li className={styles.recommended__info__item}>
                  London
                </li>
                <li className={styles.recommended__info__item}>
                  6 years
                </li>
                <li className={styles.recommended__info__item}>
                  $20,000 - $30,000 PA
                </li>
                <li className={styles.recommended__info__item}>
                  <img src={paperclip.src} alt="download" className={styles.recommended__info__download}/>
                  Alex Walling CV.pdf
                </li>
              </ul>
            </div>
          </div>
          <div className={styles.recommended__bottom}>
            <ul className={styles.recommended__tags}>
              {/*//TODO: when we have matched logic need to display 3 items*/}
              <li className={styles.recommended__tags__item}>
                Kubernetes
              </li>
              <li className={styles.recommended__tags__item}>
                Docker
              </li>
              <li className={styles.recommended__tags__item}>
                Cloud Foundry
              </li>
            </ul>
          </div>
        </div>
        <div className={styles.recommended__item}>
          <div className={styles.recommended__top}>
            <Link href="/">
              <a className={styles.recommended__top__link}>
                Candidate matched: 98%
              </a>
            </Link>
          </div>
          <div className={styles.recommended__body}>
            <img src={image.src} alt="" className={styles.recommended__body__image}/>
            <div className={styles.recommended__body__inner}>
              <p className={styles.recommended__body__title}>
                Alex Walling
              </p>
              <ul className={styles.recommended__info}>
                <li className={styles.recommended__info__item}>
                  London
                </li>
                <li className={styles.recommended__info__item}>
                  6 years
                </li>
                <li className={styles.recommended__info__item}>
                  $20,000 - $30,000 PA
                </li>
                <li className={styles.recommended__info__item}>
                  <img src={paperclip.src} alt="download" className={styles.recommended__info__download}/>
                  Alex Walling CV.pdf
                </li>
              </ul>
            </div>
          </div>
          <div className={styles.recommended__bottom}>
            <ul className={styles.recommended__tags}>
              <li className={styles.recommended__tags__item}>
                Kubernetes
              </li>
              <li className={styles.recommended__tags__item}>
                Docker
              </li>
              <li className={styles.recommended__tags__item}>
                Cloud Foundry
              </li>
              <li className={styles.recommended__tags__item}>
                Techical Sales
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Recommended