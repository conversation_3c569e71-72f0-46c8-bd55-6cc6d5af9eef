export function jobPostingGoogle (job: any) {
  let printSchema = false;
  if (job && job.status === "publish" && job.jobBoards) {
    for (let k in job.jobBoards) {
      if (job.jobBoards[k].uniqueIntegrationId === 'googleJobs08122022') {
        printSchema = true;
        break;
      }
    }
  }

  const validThrough = () => {
    const date = new Date();
    return new Date(date.getFullYear(), date.getMonth() + 3, 1).toISOString();
  }

  const employmentType = (jobType: string) => {
    switch (jobType) {
      case "Full-Time Employees":
        return 'FULL_TIME';
      case "Part-Time Employees":
        return 'PART_TIME';
      case "Temporary Employees":
      case "Temporary workers":
      case "Seasonal Employees":
        return 'TEMPORARY';
      case "Independent Contractors":
        return 'CONTRACTOR';
      default:
        return 'OTHER';
    }
  }

  const hiringOrganization = (company: any) => {
    let result = `{
    "@type": "Organization",
    "name": "${company.name}"`;

    if (company.website) {
      result += `,
    "sameAs": "${company.website}"`;
    }

    if (company.avatar) {
      result += `,
    "logo": "${company.avatar}"`;
    }

    result += `
  }`;

    return result;
  }

  const jobLocation = (job: any) => {
    /* todo: Add the "streetAddress" and "postalCode" fields when they are added on the API side */
    let locationSchema = [],
      countries = [];
    for (let k in job.locations) {
      locationSchema.push(`{
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "${job.locations[k].city}",
        "addressRegion": "${job.locations[k].st}",
        "addressCountry": "${job.locations[k].country || 'USA'}"
      }
    }`);
      countries.push(job.locations[k].country || 'USA');
    }

    let result = '';
    if (locationSchema.length) {
      result = `"jobLocation": [
    ${locationSchema.join(`,
    `)}
  ],`;

      if (job.remoteLocation) {
        const countriesUnique = countries.filter((value, index, self) => self.indexOf(value) === index);
        result += `
  "applicantLocationRequirements": [`;
        for (let i = 0; i < countriesUnique.length; i++) {
          result += `
    {
      "@type": "Country",
      "name": "${countriesUnique[i]}"
    }`;
          if ((i + 1) < countriesUnique.length) {
            result += ',';
          }
        }
        result += `
  ],
  "jobLocationType": "TELECOMMUTE",`;
      }
    }

    return result;
  }

  return (
    <>
    {
      printSchema
      ? <script type="application/ld+json" dangerouslySetInnerHTML={{
          __html:
`{
  "@context": "https://schema.org/",
  "@type": "JobPosting",
  "title": "${job.title}",
  "description": "${job.description}",
  "identifier": {
    "@type": "PropertyValue",
    "name": "${job.company.name}",
    "value": "${job.company.tenantId}"
  },
  "datePosted": "${job.createdAt}",
  "validThrough": "${validThrough()}",
  "employmentType": "${employmentType(job.jobType)}",
  "hiringOrganization": ${hiringOrganization(job.company)},
  ${jobLocation(job)}
  "baseSalary": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": {
      "@type": "QuantitativeValue",
      "minValue": "${job.salaryYearMin}",
      "maxValue": "${job.salaryYearMax}",
      "unitText": "YEAR"
    }
  }
}`
        }} />
      : ''
    }
    </>
  )
}