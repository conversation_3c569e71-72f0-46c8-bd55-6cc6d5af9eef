import styles from '../../styles/Happiness.module.scss'
import globalStyle from '../../styles/Global.module.scss'
import Image from 'next/image'

import people1 from '../../public/images/happiness/people1.png'
import people2 from '../../public/images/happiness/people2.png'
import people3 from '../../public/images/happiness/people3.png'
import people4 from '../../public/images/happiness/people4.png'
import people5 from '../../public/images/happiness/people5.png'
import people6 from '../../public/images/happiness/people6.png'
import people7 from '../../public/images/happiness/people7.png'
import people8 from '../../public/images/happiness/people8.png'
import people9 from '../../public/images/happiness/people9.png'
import people10 from '../../public/images/happiness/people10.png'
import people11 from '../../public/images/happiness/people11.png'
import people12 from '../../public/images/happiness/people12.png'

import vector1 from '../../public/images/happiness/vector1.svg'
import vector2 from '../../public/images/happiness/vector2.svg'

const Happiness = () => {
  return (
    <section className={styles.happiness}>
      <div className={styles.happiness__inner}>
        <span className={`${styles.happiness__tagline} ${globalStyle.tagline}`}>
          Happy faces
        </span>
        <h2 className={styles.happiness__headline}>
          We have spread some<span> Happiness</span>
        </h2>
        <div className={styles.happiness__body}>
          <div className={`${styles.happiness__image} ${styles.happiness__people1}`}>
            <Image src={people1}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people2}`}>
            <Image src={people2}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people3}`}>
            <Image src={people3}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people4}`}>
            <Image src={people4}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people5}`}>
            <Image src={people5}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people6}`}>
            <Image src={people6}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people7}`}>
            <Image src={people7}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people8}`}>
            <Image src={people8}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people9}`}>
            <Image src={people9}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people10}`}>
            <Image src={people10}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people11}`}>
            <Image src={people11}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__people12}`}>
            <Image src={people12}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__vector1}`}>
            <Image src={vector1}/>
          </div>
          <div className={`${styles.happiness__image} ${styles.happiness__vector2}`}>
            <Image src={vector2}/>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Happiness