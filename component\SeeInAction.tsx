import styles from '../styles/SeeInAction.module.scss'
import globalStyle from '../styles/Global.module.scss'
import { TryToBelieveInterface} from "../interfaces/HomePageInterfaces";

type Props = {
    props: TryToBelieveInterface
}

const SeeInAction = ({props}: Props) => {
    return (
        <section className={styles.seeInAction}>
            <div className={styles.seeInAction__inner}>
                <span className={`${styles.seeInAction__tagline} ${globalStyle.tagline}`}>
                    {props.tagline}
                </span>
                <h2 className={styles.seeInAction__headline}>{props.headline}</h2>
                <p className={styles.seeInAction__description}>
                    {props.description}
                </p>
                    <a href='#contactUs' className={`${styles.seeInAction__button} ${globalStyle.filledButton}`}>
                        {props.button_text}
                    </a>
                <div className={styles.seeInAction__imageWrap}>
                    <img
                        src={props.image.url}
                        alt='demo'
                        className={styles.seeInAction__image}
                    />
                </div>
            </div>
        </section>
    )
}

export default SeeInAction