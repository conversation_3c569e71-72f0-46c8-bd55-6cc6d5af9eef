import { Dispatch } from 'redux'
import { AppAction, AppActionTypes } from '../../types/app'

export const toggleMenu = (visible:boolean) => {
  return async (dispatch: Dispatch<AppAction>) => {
    dispatch({ type: AppActionTypes.MOBILE_NAVIGATION_MENU, payload: visible })
  }
}

export const changeApplyPopup = (payload: any) => {
  return { type: AppActionTypes.CHANGE_APPLY_POPUP, payload }
}

export const changeSuccessApplyPopup = (payload: boolean) => {
  return { type: AppActionTypes.CHANGE_SUCCESS_APPLY_POPUP, payload }
}