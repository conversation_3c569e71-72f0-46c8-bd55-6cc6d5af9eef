@import "config";
@import "mixins";

.integrationItem {
  width: calc(33.33% - 24px);
  box-shadow: 0 4px 20px 5px rgba(153, 158, 165, 0.1);
  border-radius: 12px;
  padding: 24px;
  background: $white;
  margin: 0 12px 52px 12px;
  @include media(md) {
    width: calc(50% - 18px);
    margin: 0 0 36px 0;
  }

  @include media(sm) {
    width: 100%;
  }

  &__head {
    display: flex;
    flex-direction: column;
    padding-bottom: 32px;
    border-bottom: 1px solid $grayTone2;
  }

  &__status {
    min-height: 35px;
    margin-bottom: 16px;
    align-self: end;
    @include media(sm){
      min-height: auto;
      margin-bottom: 0;
    }

    &__value {
      color: $black;
      background: rgba(2, 156, 226, 0.3);
      border-radius: 20px;
      padding: 4px 18px;

      &.violet {
        background: rgba(155, 147, 255, 0.4);
      }
    }
  }

  &__image {
    align-self: center;
    display: flex;
    align-items: center;
    height: 83px;
  }

  &__body {
    padding-top: 32px;
  }

  &__name {
    color: $black;
    margin-bottom: 16px;
    font-weight: 800;
    line-height: 1;
  }

  &__tagline {
    color: $grayTone4;
    font-size: 14px;
    margin-bottom: 18px;
  }

  &__description {
    font-size: 14px;
  }
}