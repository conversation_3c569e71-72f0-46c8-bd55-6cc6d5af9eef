@import "mixins";
@import "config";

.result {
  padding-top: 160px;
  overflow-x: hidden;

  &__inner {
    @include container();
    display: flex;
    justify-content: space-between;
    position: relative;

    &:after {
      content: "";
      position: absolute;
      width: 560px;
      height: 560px;
      border-radius: 50%;
      border: 50px solid #F8F9FB;
      z-index: -1;
      right: 0;
      top: 0;
      transform: translate(132px, -40px);
      @include media(md) {
        display: none;
      }
    }

    @include media(md) {
      flex-direction: column;
    }
  }

  &__left {
    max-width: 510px;
    width: 100%;
    padding: 120px 0;

    @include media(md) {
      padding: unset;
      margin-bottom: 42px;
    }
  }

  &__tagline {

  }

  &__headline {
    margin-bottom: 32px;
  }

  &__description {

  }

  &__right {
    width: calc(100% - 510px);
    padding-left: 32px;
    position: relative;
    @include media(md) {
      display: flex;
      padding-left: 0;
      width: 100%;
    }

    @include media(sm) {
      flex-direction: column;
    }
  }

  &__info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 28px;
    box-shadow: 0 0 30px rgba(53, 62, 73, 0.1);
    border-radius: 12px;
    background: $white;
    width: 174px;
    height: 174px;
    position: absolute;

    @include media(md) {
      position: relative;
      right: unset;
      top: unset;
      margin: 0 16px 12px 0;
    }

    @include media(sm) {
      width: 100%;
      height: auto;
    }

    @include media(xs) {
      max-width: 220px;
      margin: 0 auto 16px auto;
      &:last-child{
        margin-bottom: 0;
      }
    }

    &.members {
      background: $mainGreen;
      right: 48%;
      top: 16%;

      @include media(md) {
        right: unset;
        top: unset;
      }

      .result__info__value,
      .result__info__name {
        color: $white;
      }
    }

    &.jobs {
      right: 0;
      top: 0;
      @include media(md) {
        right: unset;
        top: unset;
      }
    }

    &.clients {
      right: 10%;
      top: 53%;
      @include media(md) {
        right: unset;
        top: unset;
      }
    }

    &__icon {
      width: 32px;
      height: 32px;
      margin-bottom: 16px;
    }

    &__value {
      color: $black;
      font-size: 36px;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 16px;
      @include media(xs) {
        font-size: 24px;
      }
    }

    &__name {

    }
  }
}