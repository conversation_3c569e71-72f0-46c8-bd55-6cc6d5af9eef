@import "mixins";
@import "config";

.information {
  min-width: 530px;
  max-width: 530px;
  position: sticky;
  top: 200px;
  transform: translateY(-174px);
  @include media(lg){
    transform: unset;
    top: unset;
    position: unset;
    min-width: 100%;
    width: 100%;
  }

  &__inner {
    padding: 40px;
    background: $white;
    border: 1px solid $grayTone2;
    background: $white;
    border-radius: 12px;
    @include media(xs){
      padding: 32px 16px;
    }

    .job-action{
      margin-top: 32px;
    }
  }

  &__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    &__headline {
      font-size: 20px;
      font-weight: 600;
      color: $black;
      padding-right: 16px;
      line-height: 1;
      font-family: 'Poppins', sans-serif;
    }

    &__link {
      color: $greenBlue1;
      font-size: 14px;
      line-height: 1;
      font-weight: 800;

      span {
        font-size: 20px;
      }
    }
  }

  &__list {

  }

  &__item {
    margin-bottom: 16px;
    line-height: 1.2;
    color: $grayTone6;
    font-size: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    span:not(.information__item__location) {
      color: $black;
      font-weight: 800;
    }

    &__location{
      font-weight: 400;
    }

  }

  &__social {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    @include media(xs){
      flex-direction: column;
    }

    &__left {
      @include media(xs){
        margin-bottom: 12px;
      }
    }

    &__icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;

      &.small {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }

    &__website {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__name {
      font-size: 12px;
      padding-right: 3px;
      color: $grayTone5;
    }

    &__link {
      line-height: 1;
      color: $grayTone7;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      -webkit-text-overflow: ellipsis;
      text-overflow: ellipsis;
      max-width: 150px;
      display: inline-block;

      &:hover {
        color: $mainGreen;
        text-decoration: underline;
      }
    }

    &__right {
      display: flex;
    }

    &__item {
      margin-right: 24px;
      display: block;

      &:last-child {
        margin-right: 0;
      }

      img {
        min-width: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }

}