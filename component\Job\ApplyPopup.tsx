import { store } from "../../store";
import styles from "../../styles/ApplyPopup.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import { changeApplyPopup } from "../../store/action-creator/app";
import { useTypedSelector } from "../../hook/useTypedSelector";
import { useRouter } from "next/router";

const ApplyPopup = () => {
	const { applyPopup } = useTypedSelector(state => state.app);
	const router = useRouter();
	const onClosePopup = () => {
		store.dispatch(changeApplyPopup({
			visible: false,
			jobId: 0,
			jobTitle: "",
		}));
	};

	const jobSubmit = () => {
		store.dispatch(changeApplyPopup(false))
		const expirationDate = new Date();
		expirationDate.setTime(expirationDate.getTime() + 300000); // 5 minute in milliseconds
		document.cookie = `jobId=${applyPopup.jobId}; expires=${expirationDate.toUTCString()}; path=/; domain=.urecruits.com`;
		const redirectUri = encodeURIComponent(`job/${applyPopup.jobId}/apply`)
		router.push(`/api/auth/login?redirectUri=${redirectUri}`);
	}

	return (
		<div className={`${styles.popup} ${styles.apply}`}>
			<div className={styles.popup__step}>
				<div className={styles.popup__head}>
					<p className={styles.popup__head__headline}>
						Apply to the job
					</p>
					<svg
						width="24"
						height="24"
						viewBox="0 0 24 24"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
						className={styles.popup__head__close}
						onClick={onClosePopup}
					>
						<path d="M18 6L12 12M6 18L12 12M12 12L6 6L18 18" stroke="#C1C5CB" strokeWidth="1.5"
							strokeLinecap="round"
							strokeLinejoin="round" />
					</svg>
				</div>
				<div className={styles.popup__body}>
					<p className={styles.popup__body__text}>
						Do you really want to apply for <strong>{applyPopup.jobTitle}</strong>? You can check your profile by link
						bellow, if you want to
						change some information
					</p>
					<a target="_blank" href={"https://app.urecruits.com/profile-setting"} className={styles.popup__body__link}>
						Check profile
					</a>
				</div>
				<div className={`${styles.popup__bottom} ${styles.end}`}>
					<button
						className={`${styles.popup__bottom__cancel} ${globalStyle.emptyButton}`}
						onClick={onClosePopup}
					>
						Cancel
					</button>
					<div
						className={`${styles.popup__bottom__button} ${globalStyle.filledButton}`}
						onClick={jobSubmit}
					>
						Submit
					</div>
				</div>
			</div>
		</div>
	);
};
export default ApplyPopup;