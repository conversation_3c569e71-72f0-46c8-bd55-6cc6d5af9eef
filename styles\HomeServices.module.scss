@import "config";
@import "mixins";

.services {
  &__inner {
    @include container;
    display: flex;
    justify-content: space-between;
    @include media(lg) {
      flex-direction: column;
    }
  }

  &__left {
    width: calc(45% - 43px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    @include media(lg) {
      width: 100%;
    }
  }

  &__headline {
    max-width: 510px;
    margin-bottom: 32px;
    @include media(lg) {
      max-width: unset;
    }

    @include media(sm) {
      max-width: unset;
      margin-bottom: 12px;
      line-height: 1.58;
    }
  }

  &__description {
    margin-bottom: 52px;
    max-width: 510px;
    font-weight: normal;
    position: relative;

    &:after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      height: 121px;
      width: 175px;
      transform: translate(0px, 195px);
      background: url("../public/images/icon/arrow-services-home-big_ic.svg");
      z-index: -1;
      @include media(lg) {
        background: url("../public/images/icon/arrow-services-home-small_ic.svg");
        width: 50px;
        height: 100px;
        transform: translate(0px, 100px);
      }
    }

    @include media(lg) {
      max-width: unset;
    }

    @include media(sm) {
      margin-bottom: 36px;
      line-height: 1.5;
    }
  }

  &__button {
    max-width: fit-content;
    @include media(lg) {
      margin-bottom: 62px;
    }

    @include media(xs) {
      font-size: 16px;
    }

    @include media(xss) {
      font-size: 14px;
    }
  }

  &__right {
    width: calc(55% - 43px);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    @include media(lg) {
      width: 100%;
    }
  }

  &__information {
    display: flex;
    width: calc(50% - 25px);
    flex-direction: column;
    margin-bottom: 100px;
    @include media(sm) {
      width: 100%;
      margin-bottom: 38px;
    }

    &:nth-last-of-type(-n+2) {
      margin-bottom: 0;
    }

    @include media(sm) {
      &:nth-last-of-type(-n+2) {
        margin-bottom: 38px;
      }
      &:last-child{
        margin-bottom: 0;
      }
    }

    &__wrap {
      display: flex;
      width: 100%;
      align-items: center;
      margin-bottom: 18px;
    }

    &__image {
      display: block;
      max-width: 32px;
      width: 100%;
      height: 32px;
      @include media(xs) {
        max-width: 24px;
        width: 100%;
        height: 24px;
      }
    }

    &__name {
      color: $grayTone7;
      line-height: 1.2;
      margin-left: 20px;
      width: calc(100% - 44px);
      @include media(sm) {
        width: fit-content;
        margin-left: 15px;
        line-height: 1;
      }
    }

    &__description {
      margin-bottom: 18px;
    }
  }

  &__link {
    font-size: 18px;
    line-height: 1;
    color: $mainGreen;
    transition: .3s ease-in, .3s ease-in-out;
    @include media(sm) {
      font-size: 16px;
    }

    &:hover {
      text-decoration-line: underline;
    }
  }
}