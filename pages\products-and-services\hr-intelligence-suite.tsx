import Layout from '../../component/Layout'
import {NextPage} from 'next'
import Head from 'next/head'
import ContactUs from "../../component/ContactUs";
import GetStarted from "../../component/GetStarted";
import SubServicesBanner from "../../component/SubServices/SubServicesBanner";
import SubServicesList from "../../component/SubServices/SubServicesList";
import {ServicesPageInterface, ServicesPageTopInterfaces} from "../../interfaces/HomePageInterfaces";

interface Data {
  services: ServicesPageInterface
  services_pages: ServicesPageTopInterfaces
}

const HRIntelligenceSuitePage: NextPage<Data> = ({services, services_pages}) => {
  return (
    <Layout>
      <Head>
        <title>HR Intelligence System | Human Resource Insights Suite</title>
        <meta name="description" content="Boost hiring with uRecruits' HR Intelligence Suite. Automate tasks, assess talent, and gain AI-driven insights across the entire recruitment process." />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org/",
          "@type": "SoftwareApplication",
          "name": "uRecruits HR Intelligence Suite",
          "applicationCategory": "HRSoftware",
          "applicationSubCategory": "WorkforceIntelligencePlatform",
          "operatingSystem": "Cloud, Windows, macOS",
          "downloadUrl": "https://urecruits.com/registration",
          "featureList": "https://urecruits.com/products-and-services/hr-intelligence-suite",
          "description": "uRecruits HR Intelligence Suite uses Generative AI to transform HR operations. From automated job creation, resume parsing, and intelligent scoring to candidate campaign personalization and job architecture mapping, our platform helps HR teams automate workflows, gain real-time insights, and improve hiring decisions across the talent lifecycle.",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.7",
            "reviewCount": "95",
            "bestRating": "5",
            "worstRating": "1"
          }
        })}} />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "What is HR Intelligence?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "HR Intelligence (also called HR Analytics) means using data to make smarter decisions in HR. It helps HR teams understand trends, track performance, and plan better."
              }
            },
            {
              "@type": "Question",
              "name": "What do you mean by the Intelligence Test in HR?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "An intelligence test in HR reviews how quickly someone can gain insights, solve problems, and learn new information. It also helps determine how they learn best (like visually or verbally) and what kind of work suits them—such as teamwork, training, or leadership roles."
              }
            },
            {
              "@type": "Question",
              "name": "What Does Industry Intelligence in HR mean?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Industry intelligence in HR means knowing what is trending in the market, what skills are in demand, and talent movement patterns. It helps HR teams make better plans, improve the hiring process, and manage talent more effectively."
              }
            }
          ]
        })}} />
      </Head>
      <SubServicesBanner props={services_pages.Banner}/>
      <SubServicesList props={services_pages.row}/>
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

HRIntelligenceSuitePage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const response_services_pages = await fetch('https://cms-dev.urecruits.com/hr-intelligence-suite-page')
  const services = await response_services.json()
  const services_pages= await response_services_pages.json()
  return {services, services_pages}
}

export default HRIntelligenceSuitePage