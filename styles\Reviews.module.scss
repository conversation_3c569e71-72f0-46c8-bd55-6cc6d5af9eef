@import "config";
@import "mixins";

.reviews {
  background: $milkWhite url("../public/images/reviews/bg.svg");
  margin-top: 70px;
  @include media(xs){
    margin-top: 50px;
  }

  &__inner {
    @include container;
  }

  &__headline {
    width: calc(100% - 100px);
    @include media(xs) {
      width: 100%;
    }
  }

  &__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 52px;
    @include media(xs) {
      margin-bottom: 24px;
    }
  }

  &__swiper {
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;


    &__slide {
      width: 50%;
      display: flex;
      align-items: center;
      flex-direction: column;
    }
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    @include media(xs) {
      margin-top: 40px;
      width: 100%;
    }

    &__prev {
      margin-right: 24px;
    }

    &__prev,
    &__next {
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        svg {
          path {
            stroke: $mainGreen;
          }
        }
      }

      svg {
        min-width: 11px;
        width: 11px;
        height: 26px;
        display: flex;
        flex-grow: 1;

        path {
          transition: .3s ease-in, .3s ease-in-out;
        }
      }
    }
  }

  &__comment {
    padding: 32px;
    background: $white;
    border-radius: 12px;
    border: 1px solid $grayTone2;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:after {
      content: "";
      position: absolute;
      width: 20px;
      height: 20px;
      background: $white;
      border: 1px solid #DFE2E6;
      transform: rotate(45deg);
      z-index: -1;
      bottom: -11px;
    }

    &:before {
      content: "";
      position: absolute;
      background: $white;
      width: 27px;
      height: 1px;
      bottom: -1px;
    }

    &__title {
      margin-bottom: 18px;
      align-self: flex-start;
    }

    &__desctiption {
      align-self: flex-start;
    }
  }

  &__author {
    display: flex;
    align-items: center;
    margin-top: 48px;

    &__image {
      max-width: 44px;
      width: 44px;
      height: 44px;
    }

    &__info {
      display: flex;
      flex-direction: column;
      margin-left: 16px;
    }

    &__name {
      color: $black;
      margin-bottom: 6px;
      line-height: 1;
    }

    &__work {
      font-size: 14px;
      line-height: 1;
      color: $grayTone4;
    }
  }
}