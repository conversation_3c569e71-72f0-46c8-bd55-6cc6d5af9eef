import mailchimp, {Status} from '@mailchimp/mailchimp_marketing';

mailchimp.setConfig({
	apiKey: process.env.MAILCHIMP_API_KEY,
	server: process.env.MAILCHIMP_API_SERVER
});

const audience_id:any = process.env.MAILCHIMP_AUDIENCE_ID

export default async (req:any, res:any) => {
	const { email } = req.body;

	if (!email) {
		return res.status(400).json({ error: 'Email is required' });
	}
	try {
		await mailchimp.lists.addListMember(audience_id, {
			email_address: email,
			// @ts-ignore
			status: 'subscribed'
		});

		return res.status(201).json({ error: '' });
	} catch (error:any) {
		return res.status(500).json({ error: error.message || error.toString() });
	}
};