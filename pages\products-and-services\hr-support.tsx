import Layout from '../../component/Layout'
import {NextPage} from 'next'
import Head from 'next/head'
import ContactUs from "../../component/ContactUs";
import GetStarted from "../../component/GetStarted";
import SubServicesBanner from "../../component/SubServices/SubServicesBanner";
import SubServicesList from "../../component/SubServices/SubServicesList";
import {ServicesPageInterface, ServicesPageTopInterfaces} from "../../interfaces/HomePageInterfaces";

interface Data {
  services: ServicesPageInterface
  services_pages: ServicesPageTopInterfaces
}

const HRSupportPage: NextPage<Data> = ({services, services_pages}) => {
  return (
    <Layout>
      <Head>
        <title>HR Support Services | Smart HR Help & Digital Solutions</title>
        <meta name="description" content="Get expert HR support with a knowledge base, AI chat, videos, blogs & ticketing. Simplify HR tasks and empower your team with digital tools." />
      </Head>
      <SubServicesBanner props={services_pages.Banner}/>
      <SubServicesList props={services_pages.row}/>
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

HRSupportPage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const response_services_pages = await fetch('https://cms-dev.urecruits.com/hr-support-page')
  const services = await response_services.json()
  const services_pages= await response_services_pages.json()
  return {services, services_pages}
}

export default HRSupportPage