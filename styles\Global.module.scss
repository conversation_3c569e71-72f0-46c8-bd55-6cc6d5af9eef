@import "config";
@import "mixins";

.tagline {
  font-size: 14px;
  line-height: 24px;
  font-family: '<PERSON>pins', sans-serif;
  font-weight: 500;
  color: $greenBlue1;
  margin-bottom: 12px;
  text-transform: uppercase;
  @include media(xs) {
    margin-bottom: 8px;
  }
  span{
    text-transform: lowercase;
  }
}

.emptyButton,
.disableEmptyButton {
  line-height: 1;
  padding: 13px 26px;
  color: $mainGreen;
  font-weight: 800;
  border-radius: 4px;
  border: 2px solid $mainGreen;
  transition: .3s ease-in, .3s ease-in-out;

  &:hover {
    color: $greenBlue2;
    border-color: $greenBlue2;
  }
}

.disableEmptyButton {

  &:hover {
    color: unset;
    border-color: unset;
  }
}


.filledButton,
.disableFilledButton {
  line-height: 1;
  padding: 15px 28px;
  color: $white;
  font-weight: 800;
  font-size: 20px;
  border-radius: 4px;
  background: $greenGradient2;
  transition: .3s ease-in, .3s ease-in-out;
  cursor:pointer;
  &:hover {
    background: $greenGradient1;
  }
}

.filledButton.disableFilledButton {
  background: linear-gradient(125.2deg, rgba(9, 156, 115, 0.6) 8.04%, rgba(1, 84, 98, 0.6) 127.26%);

  &:hover {
    border-color: unset;
    &:hover {
      background: linear-gradient(125.2deg, rgba(9, 156, 115, 0.6) 8.04%, rgba(1, 84, 98, 0.6) 127.26%);
    }
  }
}

