# The manifest for the "marketing-website" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: marketing-website
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  # Requests to this path will be forwarded to your service.
  # To match all requests you can use the "/" path.
  path: '/'
  # You can specify a custom health check path. The default is "/".
  # healthcheck: '/'
  alias: [ 'urecruits.com', 'www.urecruits.com' ]

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build: Dockerfile
  # Port exposed through your container to route traffic to it.
  port: 3000

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
exec: true     # Enable running commands in your container.

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
  AUTH0_BASE_URL: https://urecruits.com
#  AUTH0_ISSUER_BASE_URL: https://dev-9zt22me9.us.auth0.com
  AUTH0_ISSUER_BASE_URL: https://auth.urecruits.com
  RETURN_TO: https://app.urecruits.com/
  NEXT_PUBLIC_RECRUITMENT_API: https://recruitment-micro.urecruits.com

secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
  AUTH0_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/AUTH0_SECRET
  AUTH0_CLIENT_ID: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/AUTH0_CLIENT_ID
  AUTH0_CLIENT_SECRET: /copilot/${COPILOT_APPLICATION_NAME}/${COPILOT_ENVIRONMENT_NAME}/secrets/AUTH0_CLIENT_SECRET

# You can override any of the values defined above by environment.
#environments:
#  test:
#    count: 2               # Number of tasks to run for the "test" environment.
