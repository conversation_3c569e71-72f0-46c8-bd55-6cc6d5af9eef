@import "config";
@import "mixins";

.empty {
  display: flex;
  justify-content: center;
  align-items: center;

  &__inner{
    background: $white;
    border: 1px solid $grayTone2;
    border-radius: 12px;
    padding: 56px 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 940px;
    width: 100%;
    margin: 24px;
    @include media(xs){
      margin: 24px 16px;
      padding: 32px 24px;
    }
  }

  &__content {
    max-width: 392px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__img {
    max-width: 340px;
    width: 100%;
    margin-bottom: 32px;
  }

  &__headline {
    font-size: 36px;
    font-family: 'Poppins', sans-serif;
    color: $black;
    font-weight: 700;
    margin-bottom: 24px;
    text-align: center;
  }

  &__description {
    text-align: center;
    color: $grayTone7;
    font-size: 18px;
    margin-bottom: 52px;
    @include media(xs){
      margin-bottom: 32px;
    }
  }

  &__button {
    font-size: 14px;
  }
}