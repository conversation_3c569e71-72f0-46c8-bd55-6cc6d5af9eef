@import "config";
@import "mixins";

.pagination {
  display: flex;
  align-items: center;

  &.mobile {
    display: none;
    @include media(md) {
      display: flex;
    }
  }

  @include media(md) {
    display: none;
  }

  &__dots {
    margin: auto 5px;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &__item {
    border-radius: 6px;
    text-align: center;
    margin: 0 5px 0;
    min-width: 24px;
    padding: 2px;
    height: 24px;
    border: 1px solid #DFE2E6;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.active {
      background: $mainGreen;
      border-color: $mainGreen;
      color: #fff;
    }
  }

  .break {
    padding: 0 10px;
  }

  &__next, &__prev {
    color: #099C73;
    padding: 5px;
    line-height: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    svg {
      path {
        stroke: $mainGreen;
      }

      width: 24px;
      height: 24px;
    }

    &.disabled {
      pointer-events: none;

      svg {
        path {
          stroke: #C1C5CB;
        }
      }

      cursor: default;
    }
  }

  &__next {
    margin-left: 5px;
  }

  &__prev {
    margin-right: 5px;
  }
}