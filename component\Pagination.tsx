import {IPagination} from "../types/jobs";
import {DOTS, usePagination} from '../hook/usePagination'
import {memo} from "react";
import {useMobilePagination} from "../hook/useMobilePagination";
import styles from '../styles/Pagination.module.scss'
import {useRouter} from "next/router";
import {toJSONLocal} from "./Jobs/JobFilter";
import {useTypedSelector} from "../hook/useTypedSelector";

const Pagination = ({currentPage, limit, totalCount}: IPagination) => {
  const router = useRouter()
  const paginationRange = usePagination(totalCount, limit, 2, currentPage,);
  const paginationRangeMobile = useMobilePagination(totalCount, limit, 1, currentPage,);
  const {
    jobTitle,
    jobLocation,
    postedOnData,
    salaryYearValue,
    salaryMonthValue,
    jobType,
    experienceYearValue,
    skills,
    functionalArea,
    industryType,
    education,
    companyName,
    preferableShift,
  } = useTypedSelector(state => state.jobs.filters);


  const onChangePage = (value: number | string) => {
    router.push(decodeURI(`/jobs/?page=${value}
		&limit=${limit}
		&title=${jobTitle}
		${jobLocation ? jobLocation.map((x: any) => `&locations=${x.id}`).join("") : ""}
		&createdAt=${postedOnData ? toJSONLocal(postedOnData) : ""}
		&salaryMonthMin=${salaryMonthValue[0]}
		&salaryMonthMax=${salaryMonthValue[1]}
		&salaryYearMin=${salaryYearValue[0]}
		&salaryYearMax=${salaryYearValue[1]}
		&experienceMin=${experienceYearValue[0]}
		&experienceMax=${experienceYearValue[1]}
		&education=${education?.value ? education?.value : ""}
		${skills ? skills.map((x: any) => `&skills=${x.label}`).join("") : ""}
		&companyId=${companyName?.id ? companyName?.id : ""}
		&jobType=${jobType?.value ? jobType?.value : ""}
		&industryId=${industryType?.id ? industryType?.id : ""}
		&functionalArea=${functionalArea?.value ? functionalArea?.value : ""}
		&preferableShift=${preferableShift?.label ? preferableShift?.label : ""}
		`));
  }

  return (
    <>
      <ul className={styles.pagination}>
        <li
          className={`${styles.pagination__prev} ${currentPage > 1 ? '' : styles.disabled}`}
          onClick={() => onChangePage(currentPage - 1)}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 8L10 12L14 16" stroke="#C1C5CB" strokeWidth="1.5" strokeLinecap="round"
                  strokeLinejoin="round"/>
          </svg>
        </li>
        {paginationRange?.map((pageNumber, index) => {
          if (pageNumber === DOTS) {
            return <li key={'dots' + index} className={styles.pagination__dots}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M8.65823 14.632C8.85823 14.632 9.02623 14.704 9.16223 14.848C9.30623 14.984 9.37823 15.152 9.37823 15.352C9.37823 15.552 9.30223 15.724 9.15023 15.868C9.00623 16.004 8.84223 16.072 8.65823 16.072C8.47423 16.072 8.30623 16.004 8.15423 15.868C8.01023 15.724 7.93823 15.552 7.93823 15.352C7.93823 15.152 8.00623 14.984 8.14223 14.848C8.28623 14.704 8.45823 14.632 8.65823 14.632ZM11.9981 14.632C12.1981 14.632 12.3661 14.704 12.5021 14.848C12.6461 14.984 12.7181 15.152 12.7181 15.352C12.7181 15.552 12.6421 15.724 12.4901 15.868C12.3461 16.004 12.1821 16.072 11.9981 16.072C11.8141 16.072 11.6461 16.004 11.4941 15.868C11.3501 15.724 11.2781 15.552 11.2781 15.352C11.2781 15.152 11.3461 14.984 11.4821 14.848C11.6261 14.704 11.7981 14.632 11.9981 14.632ZM15.3379 14.632C15.5379 14.632 15.7059 14.704 15.8419 14.848C15.9859 14.984 16.0579 15.152 16.0579 15.352C16.0579 15.552 15.9819 15.724 15.8299 15.868C15.6859 16.004 15.5219 16.072 15.3379 16.072C15.1539 16.072 14.9859 16.004 14.8339 15.868C14.6899 15.724 14.6179 15.552 14.6179 15.352C14.6179 15.152 14.6859 14.984 14.8219 14.848C14.9659 14.704 15.1379 14.632 15.3379 14.632Z"
                  fill="#343B43"/>
              </svg>
            </li>;
          }
          return (
            <li
              key={pageNumber as number + index}
              className={`${styles.pagination__item} ${pageNumber === currentPage ? styles.active : ''}`}
              onClick={() => onChangePage(pageNumber)}
            >
              {pageNumber}
            </li>
          );
        })}
        <li
          onClick={() => onChangePage(currentPage + 1)}
          className={`${styles.pagination__next} ${currentPage === Math.ceil(totalCount / limit) ? styles.disabled : ''}`}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 8L14 12L10 16" stroke="#099C73" strokeWidth="1.5" strokeLinecap="round"
                  strokeLinejoin="round"/>
          </svg>
        </li>
      </ul>
      <ul className={`${styles.pagination} ${styles.mobile}`}>
        <li onClick={() => currentPage > 1}
            className={`${styles.pagination__prev} ${currentPage > 1 ? '' : styles.disabled}`}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 8L10 12L14 16" stroke="#C1C5CB" strokeWidth="1.5" strokeLinecap="round"
                  strokeLinejoin="round"/>
          </svg>
        </li>
        {paginationRangeMobile?.map((pageNumber, index) => {
          if (pageNumber === DOTS) {
            return <li key={'dots' + index} className='pagination__dots'>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M8.65823 14.632C8.85823 14.632 9.02623 14.704 9.16223 14.848C9.30623 14.984 9.37823 15.152 9.37823 15.352C9.37823 15.552 9.30223 15.724 9.15023 15.868C9.00623 16.004 8.84223 16.072 8.65823 16.072C8.47423 16.072 8.30623 16.004 8.15423 15.868C8.01023 15.724 7.93823 15.552 7.93823 15.352C7.93823 15.152 8.00623 14.984 8.14223 14.848C8.28623 14.704 8.45823 14.632 8.65823 14.632ZM11.9981 14.632C12.1981 14.632 12.3661 14.704 12.5021 14.848C12.6461 14.984 12.7181 15.152 12.7181 15.352C12.7181 15.552 12.6421 15.724 12.4901 15.868C12.3461 16.004 12.1821 16.072 11.9981 16.072C11.8141 16.072 11.6461 16.004 11.4941 15.868C11.3501 15.724 11.2781 15.552 11.2781 15.352C11.2781 15.152 11.3461 14.984 11.4821 14.848C11.6261 14.704 11.7981 14.632 11.9981 14.632ZM15.3379 14.632C15.5379 14.632 15.7059 14.704 15.8419 14.848C15.9859 14.984 16.0579 15.152 16.0579 15.352C16.0579 15.552 15.9819 15.724 15.8299 15.868C15.6859 16.004 15.5219 16.072 15.3379 16.072C15.1539 16.072 14.9859 16.004 14.8339 15.868C14.6899 15.724 14.6179 15.552 14.6179 15.352C14.6179 15.152 14.6859 14.984 14.8219 14.848C14.9659 14.704 15.1379 14.632 15.3379 14.632Z"
                  fill="#343B43"/>
              </svg>
            </li>;
          }
          return (
            <li
              key={pageNumber as number + index}
              className={`pagination__item ${pageNumber === currentPage ? 'active' : ''}`}
              onClick={() => onChangePage(pageNumber)}
            >
              {pageNumber}
            </li>
          );
        })}
        <li
          onClick={() => onChangePage(currentPage + 1)}
          className={`${styles.pagination__next} ${currentPage === Math.ceil(totalCount / limit) ? styles.disabled : ''}`}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 8L14 12L10 16" stroke="#099C73" strokeWidth="1.5" strokeLinecap="round"
                  strokeLinejoin="round"/>
          </svg>
        </li>
      </ul>
    </>
  )
}

export default memo(Pagination)