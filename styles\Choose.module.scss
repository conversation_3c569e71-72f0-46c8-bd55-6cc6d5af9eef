@import "mixins";
@import "config";

.choose {
  padding-top: 140px;

  &__inner {
    @include container;
  }

  &__tagline {
    text-align: center;
  }

  &__headline {
    max-width: 644px;
    width: 100%;
    margin: 0 auto 52px auto;
    @include media(xs) {
      text-align: center;
    }
  }

  &__list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &__item {
    width: calc(33.33% - 28px);
    border-radius: 12px;
    transition: .3s ease-in, .3s ease-in-out;

    @include media(md) {
      width: 100%;
      margin-bottom: 40px;
      &:last-child{
        margin-bottom: 0;
      }
    }
    @include media(xs) {
      margin-bottom: 76px;
    }

    &:hover {
      box-shadow: 0 4px 20px 5px rgba(153, 158, 165, 0.1);
      @include media(md) {
        box-shadow: unset;
      }

      .choose__item__more {
        opacity: 1;
      }
    }

    &__link {
      color: $grayTone5;
      display: flex;
      flex-direction: column;
      padding: 32px 30px;
      height: 100%;
      @include media(md) {
        height: unset;
        padding: 0;
      }
    }

    &__image {
      margin-bottom: 24px;
    }

    &__name {
      margin: 24px 0 18px 0;
      @include media(xs) {
        margin: 24px 0 16px 0;
      }
    }

    &__information {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;
    }

    &__description {

    }

    &__more {
      padding-top: 24px;
      color: $mainGreen;
      line-height: 1;
      opacity: 0;
      transition: .3s ease-in, .3s ease-in-out;
      @include media(md) {
        opacity: 1;
      }
    }

  }
}