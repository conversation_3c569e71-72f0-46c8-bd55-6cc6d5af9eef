@import "config";
@import "mixins";


.seeInAction{
  margin-top: 70px;
  @include media(xs){
    margin-top: 50px;
  }

  &__inner{
    @include container;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__headline{
    margin-bottom: 16px;
  }

  &__description{
    font-size: 20px;
    color: $grayTone4;
    margin-bottom: 52px;
    text-align: center;
    @include media(xs){
      margin-bottom: 36px;
      font-size: 16px;
    }
  }

  &__button{
    margin-bottom: 40px;
  }

  &__imageWrap{
    display: flex;
    justify-content: center;
    position: relative;
    align-items: flex-end;
    &:after{
      background: url("../public/images/icon/demo-image-bg_ic.svg") no-repeat;
      content: '';
      position: absolute;
      background-size: cover;
      width: 101%;
      height: 118%;
      z-index: -1;
      top:-33%;
    }
  }

  &__image{
    object-fit: contain;
  }

}