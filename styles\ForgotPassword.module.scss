@import "config";
@import "mixins";

.forgot {
  @include authSection;

  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include media(md) {
      justify-content: center;
    }
  }

  &__headline {
    margin-bottom: 37px;
    font-weight: 500;
    @include media(xs) {
      margin-bottom: 33px;
    }
  }

  &__right {
    max-width: 815px;
    width: 100%;
    height: 680px;
    display: flex;
    align-items: center;
    @include media(md) {
      display: none;
    }
  }

  &__left {
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    padding-right: 20px;


    @include media(xs) {
      flex-direction: column-reverse;
    }

    &__wrap {
      display: flex;
      flex-direction: column;
    }
  }

  &__email {
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    padding-right: 20px;

    &__headline {
      font-weight: 500;
      color: $black;
      line-height: 1.4;
      font-family: 'Poppins', sans-serif;
      font-size: 28px;
      max-width: 364px;
      margin-bottom: 40px;
    }

    &__description {
      font-size: 14px;
      line-height: 1.4;
      color: $grayTone6;
      margin-bottom: 15px;
    }

    &__descriptionGreen {
      color: $mainGreen;
    }

    &__descriptionBold {
      font-weight: 800;
    }

    &__buttonWrap {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 56px;
      @include media(xs) {
        margin-top: 20px;
      }
    }

    &__button {
      font-size: 14px;
    }

    &__image {
      max-width: 100%;
      height: 60px;
      width: 60px;
      margin-bottom: 16px;

      @include media(xs) {
        margin-bottom: 18px;
      }
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    margin-bottom: 100px;
    @include media(xs) {
      margin-bottom: 0;
    }

    &__label {
      @include label;
    }

    &__input {
      @include input;
      margin-bottom: 32px;
      @include media(xs) {
        margin-bottom: 36px;
      }
    }

    &__buttonWrap {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    &__button {
      font-size: 14px;
    }
  }

  &__back {
    font-size: 14px;
    line-height: 1;
    color: $mainGreen;
    transition: .3s ease-in, .3s ease-in-out;
    @include media(xs) {
      margin-bottom: 60px;
    }

    &:hover {
      text-decoration: underline;
    }
  }
}