import { getServerSideSitemap } from 'next-sitemap'

export const getServerSideProps = async (ctx:any) => {
  const pages:any = []
  const jobsReq = await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/job`)
  const jobs = await jobsReq.json()
  jobs?.rows?.forEach((item:any) => {
    const field = {
      loc: `https://urecruits.com/job/${item.id}/`,
      lastmod: new Date().toISOString(),
      changefreq: 'monthly',
      priority: 0.7,
    }
    pages.push(field)
  })
  return getServerSideSitemap(ctx, pages)
}

// Default export to prevent next.js errors
export default function Sitemap () {}