import {NextPage} from 'next'
import globalStyles from '../styles/Global.module.scss'
import styles from '../styles/ForgotPassword.module.scss'
import right from '../public/images/icon/forgot-password-rigt_ic.svg'
import email_ic from '../public/images/icon/forgot-password-right-email_ic.svg'
import chat from '../public/images/icon/chat_ic.svg'
import AuthLayout from "../component/Auth/AuthLayout";
import Image from "next/image";
import Link from "next/link";
import {useEffect, useState} from "react";

const ForgotPassword: NextPage = () => {
  const [disabledButton, setDisabledButton] = useState(true)
  const [email, setEmail] = useState('')
  const [step, setStep] = useState('1')

  useEffect(() => {
    if (email !== '' && disabledButton) {
      setDisabledButton(false)
    } else {
      if (email === '' && !disabledButton) {
        setDisabledButton(true)
      }
    }
  }, [email]);

  return (
    <AuthLayout>
      <section className={styles.forgot}>
        {
          step === '1' ?
            <div className={styles.forgot__inner}>
              <div className={styles.forgot__left}>
                <div className={styles.forgot__left__wrap}>
                  <h2 className={styles.forgot__headline}>Forgot password</h2>
                  <form
                    className={styles.forgot__form}
                    onSubmit={() => setStep('2')}
                  >
                    <label
                      className={styles.forgot__form__label}
                      htmlFor='email'
                    >
                      Email
                    </label>
                    <input
                      type='email'
                      className={styles.forgot__form__input}
                      value={email}
                      id='email'
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      placeholder='Enter your email'
                    />
                    <div className={styles.forgot__form__buttonWrap}>
                      <button
                        type="submit"
                        className={`${disabledButton ? globalStyles.disableFilledButton : globalStyles.filledButton} ${styles.forgot__form__button}`}>
                        Reset password
                      </button>
                    </div>
                  </form>
                </div>
                <Link href='/login'>
                  <a className={styles.forgot__back}>Back to Login</a>
                </Link>
              </div>
              <div className={styles.forgot__right}>
                <Image src={right} alt='forgot-password'/>
              </div>
            </div> :
            null
        }
        {
          step === '2' ?
            <div className={styles.forgot__inner}>
              <div className={styles.forgot__email}>
                <div className={styles.forgot__email__image}>
                  <Image src={chat} alt='chat'/>
                </div>
                <p className={styles.forgot__email__headline}>
                  Please check your email for a reset password link
                </p>
                <p className={styles.forgot__email__description}>
                  We've sent a reset password link to <span
                  className={styles.forgot__email__descriptionGreen}> {email}</span> You need to create a new password
                  to get access to your account.
                </p>
                <p className={styles.forgot__email__description}>
                  Your reset password link will expire <span className={styles.forgot__email__descriptionBold}>in 2 hours</span>,
                  if you still haven't received the link please click the button below to resend it.
                </p>
                <div className={styles.forgot__email__buttonWrap}>
                  <button
                    className={`${globalStyles.filledButton} ${styles.forgot__email__button}`}>
                    Resend email
                  </button>
                </div>
              </div>
              <div className={styles.forgot__right}>
                <Image src={email_ic} alt='forgot-password'/>
              </div>
            </div> :
            null
        }
      </section>
    </AuthLayout>
  )
}

export default ForgotPassword