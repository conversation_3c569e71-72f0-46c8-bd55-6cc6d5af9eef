import SupportLayout from "../../component/SupportLayout";
import {FaqSupportInterface, HelpCenterInterface} from "../../interfaces/HomePageInterfaces";
import {NextPage} from "next";
import FaqSupport from "../../component/FaqSupport";
import router from "next/router";

interface Data {
	bannerData: HelpCenterInterface,
	support: FaqSupportInterface
}

// const Articles: NextPage<Data> = ({bannerData, support}) => {

// 	return (
// 		<SupportLayout bannerData={bannerData.Banner}>
// 			<FaqSupport support={support}/>
// 		</SupportLayout>
// 	);
// };

const Articles: any = () => {
	return router.push('https://resources.urecruits.com');
};


export default Articles;

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
	const supportPageRes = await fetch("https://cms-dev.urecruits.com/faq-support-tab");

	return {
		props: {
			bannerData: await helpCenterPageRes.json(),
			support: await supportPageRes.json(),
		},
	};
}

