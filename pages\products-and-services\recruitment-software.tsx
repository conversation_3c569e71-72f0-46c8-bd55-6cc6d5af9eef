import Layout from '../../component/Layout'
import {NextPage} from 'next'
import Head from 'next/head'
import ContactUs from "../../component/ContactUs";
import GetStarted from "../../component/GetStarted";
import SubServicesBanner from "../../component/SubServices/SubServicesBanner";
import SubServicesList from "../../component/SubServices/SubServicesList";
import {ServicesPageInterface, ServicesPageTopInterfaces} from "../../interfaces/HomePageInterfaces";

interface Data {
  services: ServicesPageInterface
  services_pages: ServicesPageTopInterfaces
}

const RecruitmentPage: NextPage<Data> = ({services, services_pages}) => {
  return (
    <Layout>
      <Head>
        <title>Agentic AI Recruitment Software | Automated Hiring Solutions</title>
        <meta name="description" content="Improve your hiring process with uRecruits' Agentic AI Recruitment software. Automate recruitment & manage job posting more efficiently with our hiring software." />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org/",
          "@type": "SoftwareApplication",
          "name": "uRecruits Agentic AI Recruitment Software",
          "applicationCategory": "HRSoftware",
          "applicationSubCategory": "ApplicantTrackingSystem",
          "operatingSystem": "Cloud, Windows, macOS",
          "downloadUrl": "https://urecruits.com/registration",
          "featureList": "https://urecruits.com/products-and-services/recruitment-software",
          "description": "Improve your hiring process with uRecruits' Agentic AI Recruitment Software. Automate recruitment workflows and manage job posting more efficiently with our all-in-one hiring platform.",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "180",
            "bestRating": "5",
            "worstRating": "1"
          }
        })}} />
        
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "Why do I need a recruitment software?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Recruitment software automates and streamlines hiring, reducing delays, errors, and missed talent. It saves time, improves accuracy, and helps you hire top candidates faster and more efficiently."
              }
            },
            {
              "@type": "Question",
              "name": "Why do I need automation in recruitment?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Traditional recruitment is slow, prone to errors, and can get mundane real quick. Automation successfully completes the mundane tasks like parsing resumes, sharing updates, scheduling interviews, and sending offer letters to selected candidates."
              }
            },
            {
              "@type": "Question",
              "name": "What makes uRecruits stand out from the competition?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Our recruitment software is Agentic AI-powered, meaning it acts autonomously, making decisions and taking actions to achieve specific goals with limited human supervision. Our tool offers an all-in-one solution to all of your hiring needs, from finding a suitable candidate to tracking employee satisfaction, all while being extremely easy-to-use and highly customizable."
              }
            },
            {
              "@type": "Question",
              "name": "Do uRecruits provide software free trial?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "Absolutely! We offer a free trial to help you test out our software and see if we are the right fit for your needs."
              }
            },
            {
              "@type": "Question",
              "name": "How much does uRecruits cost?",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "We offer flexible plans. To get a quote, contact our sales team today."
              }
            }
          ]
        })}} />
      </Head>
      <SubServicesBanner props={services_pages.Banner}/>
      <SubServicesList props={services_pages.row}/>
      <GetStarted props={services}/>
      <ContactUs/>
    </Layout>
  )
}

RecruitmentPage.getInitialProps = async () => {
  const response_services = await fetch('https://cms-dev.urecruits.com/services')
  const response_services_pages = await fetch('https://cms-dev.urecruits.com/recruitment-page')
  const services = await response_services.json()
  const services_pages= await response_services_pages.json()
  return {services, services_pages}
}

export default RecruitmentPage