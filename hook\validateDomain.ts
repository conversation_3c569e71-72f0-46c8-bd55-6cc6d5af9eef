const domainPattern = /^[a-z0-9-]{3,}$/;

export const validateDomain = (domain: string): { message: string | null } => {
  if(!domain){
    return { message: "Domain is required." };
  }
  if (domain.length < 3) {
    return { message: "The domain name must be at least 3 characters long." };
  }

  if (/\s/.test(domain)) {
    return { message: "Spaces are not allowed in the domain name." };
  }

  if (/[A-Z]/.test(domain)) {
    return { message: "The domain name cannot contain uppercase letters." };
  }

  const specialCharMatch = domain.match(/[^a-z0-9-]/);
  if (specialCharMatch) {
    return {
      message: `Special characters are not allowed in the domain name except for hyphens(-).`,
    };
  }

  if (/^-|-$/.test(domain)) {
    return { message: "The domain name cannot start or end with a hyphen (-)." };
  }

  return { message: null };
};
