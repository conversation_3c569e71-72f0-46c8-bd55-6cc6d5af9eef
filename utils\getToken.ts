import { NextApiRequest, NextApiResponse } from 'next'
import { getAccessToken } from '@auth0/nextjs-auth0'
import jwtDecode from 'jwt-decode'

export default async function getToken (req: NextApiRequest, res: NextApiResponse) {
  try {
    const { accessToken } = await getAccessToken(req, res)
    if (accessToken) {
      return jwtDecode<any>(accessToken)
    }
    return false
  } catch (e) {
    return false
  }
}