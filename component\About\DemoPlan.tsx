import styles from "../../styles/GetStarted.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import Link from "next/link";
import { useUser } from "@auth0/nextjs-auth0";

const DemoPlan = ({ props }: any) => {
  const { user } = useUser();

  return (
    <section className={`${styles.getStarted} ${styles.demoPlan}`}>
      <div className={styles.getStarted__inner}>
        <span className={`${styles.getStarted__tagline} ${globalStyle.tagline}`}>{props.tagline}</span>
        <h2 className={styles.getStarted__headline}>{props.headline}</h2>
        <h5 className={styles.getStarted__description}>{props.description}</h5>
        <div className={styles.getStarted__block}>
          <div className={styles.getStarted__block__left}>
            {user ? (
              <a href="https://app.urecruits.com/" className={`${styles.getStarted__block__buttonFree} ${globalStyle.filledButton}`}>
                {props.demo_get_started[0].button}
              </a>
            ) : (
              <Link href="/registration">
                <a className={`${styles.getStarted__block__buttonFree} ${globalStyle.filledButton}`}> {props.demo_get_started[0].button}</a>
              </Link>
            )}
            <p className={styles.getStarted__block__description}>{props.demo_get_started[0].tagline}</p>
          </div>
          <div className={styles.getStarted__block__right}>
            <a href="#contactUs" className={`${styles.getStarted__block__buttonDemo} ${globalStyle.emptyButton}`}>
              {props.demo_get_started[1].button}
            </a>
            <p className={styles.getStarted__block__description}> {props.demo_get_started[1].tagline}</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DemoPlan;
