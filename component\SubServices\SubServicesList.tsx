import styles from '../../styles/SubServicesList_.module.scss'
import Link from 'next/link'
import {memo, useEffect, useRef, useState} from 'react'
import globalStyle from '../../styles/Global.module.scss'
import {ServicesPageRowInterface} from "../../interfaces/HomePageInterfaces";


type Props = {
    props: Array<ServicesPageRowInterface>
}

const SubServicesList = ({props}: Props) => {


    return (
        <section className={styles.section}>
                {
                    props?.map((item, index) => {
                        return <div className={styles.row} key={index}>
                            <div className={styles.inner}>
                            <p className={`${styles.tagline} ${globalStyle.tagline}`}>{item.tagline}</p>
                            <h3 className={styles.headline}>{item.headline}</h3>
                            <div className={styles.list}>
                                {
                                    item.Features?.map((value, index) => {
                                        return <div className={styles.item} key={index}>
                                            <img src={value.image?.url} className={styles.item__image} alt={'services'}/>
                                            <p className={styles.item__title}>{value.name}</p>
                                            <p className={styles.item__desc}>{value.description}</p>
                                        </div>
                                    })
                                }
                            </div>
                        </div>
                        </div>
                    })
                }
        </section>
    )
}

export default memo(SubServicesList)