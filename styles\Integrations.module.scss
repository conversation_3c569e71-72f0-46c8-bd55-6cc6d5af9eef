@import "config";
@import "mixins";

.integrations {
  @include media(xs) {
    padding-right: 16px;
    padding-left: 16px;
  }

  &__inner {
    @include container;
    padding: 0;

    @include media_min(xs) {
      padding-right: 0;
      padding-left: 0;
    }
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    @include media(md) {
      justify-content: space-between;
    }
  }

  &__mobileFilter {
    display: none;
    @include media(md) {
      display: block;
      margin-bottom: 40px;
    }
  }

  &__filter {
    display: flex;
    margin-bottom: 140px;

    @include media(md) {
      display: none;
    }

    &.animated {
      width: 100%;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 2;
      padding: 42px 0;
      background-color: $milkWhite;
    }

    &__inner {
      @include container;
      display: flex;
    }

    &__tagline {
      text-transform: uppercase;
      font-family: Poppins, sans-serif;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      color: $grayTone5;
      white-space: nowrap;
      padding-right: 14px;
    }

    &__items {
      display: flex;
      overflow-x: scroll;
      flex-wrap: nowrap;
      justify-content: space-between;
      margin: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      &::-webkit-scrollbar-track {
        background: $white;

      }

      &::-webkit-scrollbar-thumb {
        background: $white;

      }

      &::-webkit-scrollbar-thumb:hover {
        background: $white;
      }
    }

    &__link {
      white-space: nowrap;
      margin: 0 12px;
      font-size: 14px;
      color: $grayTone6;
      cursor: pointer;
      transition: .3s ease-in, .3s ease-in-out;

      &:hover {
        color: $mainGreen;
      }

      &.active {
        color: $mainGreen;
      }
    }
  }
  &__searchError{
    &__headline{
      span{
        color: $mainGreen;
      }
    }

    &__tagline{

    }
  }
}