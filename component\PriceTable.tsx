import styles from '../styles/PriceTable.module.scss'
import Image from 'next/image'
import availableIcon from '../public/images/icon/available_ic.svg'
import globalStyle from '../styles/Global.module.scss'
import availableWhiteIcon from '../public/images/icon/available_white_ic.svg'
import {useEffect, useRef, useState} from 'react'
import PriceTableItem from './PriceTableItem'
import stickyScroll from '../hook/StickyScroll'
import Link from 'next/link'
import {NextComponentType} from "next";
import {PlanInterface} from "../interfaces/HomePageInterfaces";
import { useUser } from '@auth0/nextjs-auth0'

type Props = {
  props: Array<PlanInterface>
}
const PriceTable = ({props}: Props) => {
  const { user } = useUser()

  const [period, setPeriod] = useState('monthly')
  const [activeTab, setActiveTab] = useState(props[0].id)

  const scrollRef = useRef<HTMLDivElement>(null)
  const [fixedPackage, setFixedPackage] = useState(false)

  const stickyHandler = async () => {
    if (scrollRef.current) {
      let distance: number
      window.innerWidth < 992 ? distance = scrollRef.current.offsetTop - 400 : distance = 515
      const sticky = await stickyScroll(scrollRef.current, distance, fixedPackage)
      if (sticky !== null) setFixedPackage(() => {
        return sticky
      })
    }

  }

  useEffect(() => {
    if (scrollRef.current !== null) {
      window.addEventListener('scroll', stickyHandler)
    }

    return () => {
      window.removeEventListener('scroll', stickyHandler)
    }
  }, [])

  const getStyle = () => {
    if (fixedPackage) return styles['table__content'] + ' ' + styles['animated']
    else return styles['table__content']
  }

  return (
    <section className={styles.table}>
      <div className={styles.table__inner}>
        <form className={styles.table__period}>
          <div className={styles.table__period__item}>
            <input
              type="radio"
              name="period"
              id="monthly"
              className={styles.table__period__checkbox}
              value="monthly"
              onChange={() => setPeriod('monthly')}
              checked={period === 'monthly'}
            />
            <label htmlFor="monthly" className={styles.table__period__label}>Monthly</label>
          </div>
          <div className={styles.table__period__item}>
            <input
              type="radio"
              className={styles.table__period__checkbox}
              name="period"
              id="yearly"
              value="yearly"
              onChange={() => setPeriod('yearly')}
              checked={period === 'yearly'}
            />
            <label htmlFor="yearly" className={styles.table__period__label}>Yearly</label>
          </div>
        </form>
        <div className={styles.table__container}>
          <div className={getStyle()}>
            <div className={styles.table__packages}>
              {props && props.map((item, index) => {
                return (
                  <button
                    className={`${styles.table__packages__item} ${activeTab === item.id ? styles.active : null}`}
                    onClick={() => setActiveTab(item.id)} key={index}
                  >
                    {item.name}
                  </button>
                )
              })}
              <button
                className={`${styles.table__packages__item}`}
                onClick={() => {
                  const customPlanElement = Array.from(document.querySelectorAll('span')).find(el => el.textContent?.startsWith("Contact us"));
                  if (customPlanElement) {
                    customPlanElement.scrollIntoView();
                  }
                }}
              >
                Custom Package
              </button>
            </div>
            <div className={styles.table__tabs}>
              {
                props&&props.map((item,index)=>{
                  return(
                    item.id===activeTab&&
                  <div className={styles.table__tabs__item} key={index}>
                    <div className={styles.table__info}>
                      <div className={styles.table__info__body}>
                        <span className={styles.table__info__name}>
                        {item.Tier[0].name}
                      </span>
                        <p className={styles.table__info__price}>
                          {period==='monthly'?item.Tier[0].price_monthly:item.Tier[0].price_yearly}
                          <span>/month</span>
                        </p>
                        <p className={styles.table__info__time}>
                          per month
                        </p>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={styles.table__info__trial}>
                              {item.Tier[0].trial}
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={styles.table__info__trial}>
                                {item.Tier[0].trial}
                              </a>
                            </Link>
                        }
                        <ul className={styles.table__info__list}>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_1}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_2}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_3}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_4}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_5}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[0].pros_6}
                          </li>
                        </ul>
                      </div>
                      <div className={styles.table__info__bottom}>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                              Get started
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                                Get started
                              </a>
                            </Link>
                        }
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                              Get
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a
                                className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                                Get
                              </a>
                            </Link>
                        }
                      </div>
                    </div>
                    <div className={`${styles.table__info} ${styles.popular}`}>
                      <div className={styles.table__info__body}>
                        <span className={styles.table__info__name}>
                          {item.Tier[1].name}
                        </span>
                        <p className={styles.table__info__price}>
                          {period==='monthly'?item.Tier[1].price_monthly:item.Tier[1].price_yearly}
                          <span>/month</span>
                        </p>
                        <p className={styles.table__info__time}>
                          per month
                        </p>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={styles.table__info__trial}>
                              {item.Tier[1].trial}
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={styles.table__info__trial}>
                                {item.Tier[1].trial}
                              </a>
                            </Link>
                        }
                        <ul className={styles.table__info__list}>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_1}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_2}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_3}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_4}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_5}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableWhiteIcon}/>
                            </div>
                            {item.Tier[1].pros_6}
                          </li>
                        </ul>

                      </div>
                      <div className={styles.table__info__bottom}>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                              Get started
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                                Get started
                              </a>
                            </Link>
                        }
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                              Get
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a
                                className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                                Get
                              </a>
                            </Link>
                        }
                      </div>
                    </div>
                    <div className={styles.table__info}>
                      <div className={styles.table__info__body}>
                        <span className={styles.table__info__name}>
                        {item.Tier[2].name}
                      </span>
                        <p className={styles.table__info__price}>
                          {period==='monthly'?item.Tier[2].price_monthly:item.Tier[2].price_yearly}
                          <span>/month</span>
                        </p>
                        <p className={styles.table__info__time}>
                          per month
                        </p>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={styles.table__info__trial}>
                              {item.Tier[2].trial}
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={styles.table__info__trial}>
                                {item.Tier[2].trial}
                              </a>
                            </Link>
                        }
                        <ul className={styles.table__info__list}>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_1}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_2}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_3}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_4}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_5}
                          </li>
                          <li className={styles.table__info__item}>
                            <div className={styles.table__info__icon}>
                              <Image src={availableIcon}/>
                            </div>
                            {item.Tier[2].pros_6}
                          </li>                          
                        </ul>
                      </div>
                      <div className={styles.table__info__bottom}>
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                              Get started
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a className={`${styles.table__info__button} ${globalStyle.emptyButton}`}>
                                Get started
                              </a>
                            </Link>
                        }
                        {
                          user ?
                            <a href="https://app.urecruits.com/profile-setting" className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                              Get
                            </a>
                            :
                            <Link
                              href={'/registration'}
                            >
                              <a
                                className={`${styles.table__info__button} ${styles.animatedButton} ${globalStyle.emptyButton}`}>
                                Get
                              </a>
                            </Link>
                        }
                      </div>
                    </div>
                  </div>
                  )
                })
              }
            </div>
          </div>
          <div className={styles.table__detail} ref={scrollRef}>
            {
              props&&props.map((item,index)=>{
                return activeTab===item.id&&item.compare.map((tables,index)=>{
                  return(
                    <PriceTableItem key={index} table={tables}/>
                  )
                })
              })
            }
          </div>
        </div>
      </div>
    </section>
  )
}

export default PriceTable