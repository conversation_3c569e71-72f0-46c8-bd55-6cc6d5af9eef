import SupportLayout from "../../../component/SupportLayout";
import { HelpCenterInterface } from "../../../interfaces/HomePageInterfaces";
import { NextPage } from "next";

interface Data {
  bannerData: HelpCenterInterface;
}

const Blog: NextPage<Data> = ({ bannerData }) => {
  return (
    <SupportLayout bannerData={bannerData.Banner}>
      <div className="inline-flex flex-col items-start gap-8;">
        <span className="text-sm text-[#029CA5] font-medium leading-6 uppercase font-family: Poppins">Latest Articles</span>
      </div>
    </SupportLayout>
  );
};

export default Blog;

export async function getServerSideProps(context: { res: any; req: any; query: any }) {
  const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");

  return {
    props: {
      bannerData: await helpCenterPageRes.json(),
    },
  };
}
