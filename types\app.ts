export interface AppState {
  visibleMobileMenu: boolean,
  applyPopup: {
    visible: boolean,
    jobId: number,
    jobTitle: string
  },
  successApplyPopup: boolean
}

export enum AppActionTypes{
  MOBILE_NAVIGATION_MENU ='MOBILE_NAVIGATION_MENU',
  CHANGE_APPLY_POPUP='CHANGE_APPLY_POPUP',
  CHANGE_SUCCESS_APPLY_POPUP='CHANGE_SUCCESS_APPLY_POPUP',
}

interface MobileAppAction{
  type: AppActionTypes.MO<PERSON>LE_NAVIGATION_MENU;
  payload: boolean;

}

export type AppAction = MobileAppAction