@import "config";
@import "mixins";

.banner {
  padding-top: 182px;
  background: $milkWhite
    url("../public/images/home-banner/home-banner-bg_ic.svg");
  margin-top: -122px;

  @include media(sm) {
    padding-top: 104px;
    margin-top: -96px;
  }

  &__inner {
    @include container;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include media(md) {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  &__left {
    width: calc(50% - 110px);
    @include media(lg) {
      width: calc(50% - 40px);
    }
    @include media(md) {
      width: 100%;
    }
  }

  &__right {
    width: 50%;
    max-width: 643px;
    height: 100%;
    @include media(md) {
      margin-top: 40px;
      width: 100%;
    }
  }

  &__headline {
    margin-bottom: 28px;
    max-width: 538px;
    @include media(lg) {
      max-width: unset;
      margin-bottom: 16px;
    }
  }

  &__description {
    margin-bottom: 52px;
    font-size: 20px;
    line-height: 1.5;
    max-width: 508px;

    @include media(xs) {
      font-size: 16px;
      margin-bottom: 36px;
    }
  }

  &__buttons {
    display: flex;
    align-items: center;
    margin-bottom: 44px;
    @include media(sm) {
      margin-bottom: 28px;
    }

    &__image {
      max-width: 40px;
      height: 40px;
      width: 100%;

      circle {
        transition: 0.3s ease-in, 0.3s ease-in-out;
      }
    }

    &__start {
      margin-right: 24px;

      @include media(sm) {
        margin-right: 20px;
      }

      @include media(xs) {
        font-size: 16px;
      }
    }

    &__demo {
      display: flex;
      font-size: 20px;
      align-items: center;
      @include media(xs) {
        font-size: 16px;
      }

      &:hover {
        .banner__buttons__image {
          circle {
            fill: $greenBlue2;
          }
        }
      }

      span {
        margin-left: 16px;
        color: $black;
      }
    }
  }

  &__information {
    display: flex;
    align-items: center;
    position: relative;

    &:after {
      content: "";
      width: 22px;
      height: 48px;
      background-size: cover;
      position: absolute;
      left: 0;
      bottom: 0;
      background: url("../public/images/icon/banner-arrow_ic.svg");
      transform: translate(-130%, -85%);
    }

    &__image {
      width: 40px;
      min-width: 40px;
      height: 40px;
      object-fit: contain;
      transition: 0.3s ease-in, 0.3s ease-in-out;
    }

    &__text {
      max-width: 180px;
      margin-left: 8px;
      font-size: 14px;
      @include media(xs) {
        font-size: 12px;
        max-width: 148px;
      }
    }
  }
}