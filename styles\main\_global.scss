
body {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.5;
  font-family: 'Avenir LT Std', sans-serif;
  color: $grayTone5;
  @include media(xs) {
    font-size: 16px;
  }

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}


h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  font-weight: 700;
  color: $black;
  line-height: 1.4;
  font-family: 'Poppins', sans-serif;
}

h1, .h1 {
  font-size: 48px;
  @include media(lg) {
    font-size: 38px;
  }
  @include media(xs) {
    font-size: 24px;
  }
}

h2, .h2 {
  font-size: 36px;
  @include media(lg) {
    font-size: 36px;
  }
  @include media(xs) {
    font-size: 26px;
  }
}

h3, .h3 {
  font-size: 24px;
  @include media(lg) {
    font-size: 22px;
  }
  @include media(xs) {
    font-size: 20px;
  }
}

h4, .h4 {
  font-size: 22px;
  @include media(lg) {
    font-size: 20px;
  }
  @include media(xs) {
    font-size: 18px;
  }
}

h5, .h5 {
  font-size: 20px;
}

h6, .h6 {
  font-size: 18px;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.875em;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

img,
svg {
  vertical-align: middle;
}

picture {
  width: 100%;

  img {
    display: block;
  }
}

.main {
  min-height: 100vh;
}
body.no-scroll{
  overflow: hidden;
}

section {
  padding: 70px 0;
  @include media(xs) {
    padding: 50px 0;
  }
}