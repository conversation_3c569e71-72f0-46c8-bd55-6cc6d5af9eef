import { NextPage } from 'next'
import globalStyles from '../styles/Global.module.scss'
import styles from '../styles/Login.module.scss'
import loginRight from '../public/images/icon/login-right_ic.svg'
import Image from "next/image";
import Link from 'next/link'
import AuthLayout from "../component/Auth/AuthLayout";
// import linkedin from '../public/images/icon/linkedin-logo_ic.png'
// import google from '../public/images/icon/google-icon_ic.png'
// import facebook from '../public/images/icon/facebook-icon_ic.png'
import { useEffect, useState } from "react";
import showPassword from '../public/images/icon/open_password_ic.svg';
import hidePassword from '../public/images/icon/hide_password_ic.svg';

const Login: NextPage = () => {
  const [saveCheckbox, setSaveCheckbox] = useState(false)
  const [error, setError] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [disabledButton, setDisabledButton] = useState(true)
  const [isShowPassword, setIsShowPassword] = useState(false);
  useEffect(() => {
    if (password !== '' && email !== '' && disabledButton) {
      setDisabledButton(false)
    } else {
      if (!disabledButton && password === '' && email === '') {
        setDisabledButton(true)
      }
    }
  }, [password, email]);

  return (
    <AuthLayout>
      <section className={styles.login}>
        <div className={styles.login__inner}>
          <div className={styles.login__left}>
            <h2 className={styles.login__headline}>
              Login
            </h2>
            {/* <div className={styles.login__alternative}>
              <div className={styles.login__alternative__button}>
                <div className={styles.login__alternative__icon}>
                  <Image src={linkedin} alt='linkedin' />
                </div>
              </div>
              <div className={styles.login__alternative__button}>
                <div className={styles.login__alternative__icon}>
                  <Image src={google} alt='google' />
                </div>
              </div>
              <div className={styles.login__alternative__button}>
                <div className={styles.login__alternative__icon}>
                  <Image src={facebook} alt='facebook' />
                </div>
              </div>
            </div> */}
            <form className={styles.login__form}>
              <div className={`${styles.login__form__item} ${error ? styles.error : ''}`}>
                <label className={styles.login__form__label}>Email</label>
                <input
                  type='email'
                  className={styles.login__form__input}
                  required
                  value={email}
                  placeholder='Enter your email'
                  onChange={(e) => setEmail(e.target.value)}
                />
                <p className={styles.errorMessage}>
                  Error
                </p>
              </div>
              <div className={`${styles.login__form__item} ${error ? styles.error : ''}`}>
                <label className={styles.login__form__label}>Password</label>
                <input
                  type={isShowPassword ? 'text' : 'password'}
                  className={styles.login__form__input}
                  required
                  placeholder='Enter your password'
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <img src={isShowPassword ? hidePassword.src : showPassword.src} alt="password visibility" className={styles.login__form__password_visibility} onClick={()=>setIsShowPassword(prev=>!prev)}/>
                <p className={styles.errorMessage}>
                  Error
                </p>
              </div>
              <div className={styles.login__form__items}>
                <div className={styles.login__form__save}>
                  <div className={styles.login__form__checkbox}>
                    <input
                      id='saveCheckbox'
                      type='checkbox'
                      checked={saveCheckbox}
                      onChange={() => setSaveCheckbox(prev => !prev)}
                    />
                    <label htmlFor='saveCheckbox'><span /></label>
                  </div>
                  <span className={styles.login__form__saveText} onClick={() => setSaveCheckbox(prev => !prev)}>Remember Me</span>
                </div>
                <Link href='/forgot-password'>
                  <a className={styles.login__form__link}>
                    Forgot your password?
                  </a>
                </Link>
              </div>
              <div className={styles.login__form__buttonWrap}>
                <button
                  className={`${disabledButton ? globalStyles.disableFilledButton : globalStyles.filledButton} ${styles.login__form__button}`}>Login
                </button>
              </div>
            </form>
          </div>
          <div className={styles.login__right}>
            <Image src={loginRight} alt='login' className={styles.login__image} />
          </div>
        </div>
      </section>
    </AuthLayout>
  )
}

export default Login