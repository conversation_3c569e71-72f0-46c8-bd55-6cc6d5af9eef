@import "config";
@import "mixins";

.footer {
  background: url("../public/images/footer/bg.svg"), $milkWhite;
  margin-top: 70px;
  @include media(xs) {
    margin-top: 50px;
  }

  &__inner {
    @include container;
  }

  &__top {
    padding: 64px 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    @include media(xs) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &__subscription {
    max-width: 360px;
    width: 100%;
    @include media(md) {
      margin-bottom: 40px;
    }

    &__logo {
      margin-bottom: 32px;
      display: block;
    }

    &__text {
      margin-bottom: 20px;
      color: $grayTone6;
      @include media(xs) {
        margin-bottom: 16px;
      }
    }

    &__form {
      width: 100%;
      position: relative;
    }

    &__error {
      @include error-message;
      display: block;
    }
    &__success {
      @include error-message;
      display: block;
      color: $mainGreen;
    }

    &__input {
      border: 1px solid $grayTone2;
      box-sizing: border-box;
      border-radius: 4px;
      background: $white;
      width: 100%;
      padding: 13px 56px 13px 16px;
      color: $grayTone4;

      &::placeholder {
        color: $grayTone4;
      }
    }

    &__submit {
      width: 20px;
      height: 20px;
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(-18px, 18px);
    }
  }

  &__nav {
    max-width: 168px;
    width: 100%;

    @include media(md) {
      margin-bottom: 40px;
    }

    &__headline {
      color: $grayTone6;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 32px;
    }

    &__list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 32px;
    }

    &__item {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__link {
      color: $grayTone6;
      font-size: 16px;
      line-height: 1;
      transition: 0.3s ease-in, 0.3s ease-out;

      &:hover {
        color: $mainGreen;
        text-decoration: underline;
      }
    }
  }

  &__contact {
    display: flex;

    &__inner {
      min-width: 204px;
    }

    &__headline {
      color: $grayTone6;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 32px;
    }

    &__item {
      margin-bottom: 32px;
    }

    &__link {
      color: $grayTone6;
      display: flex;
      align-items: center;
      min-height: 29px;
      transition: 0.3s ease-in, 0.3s ease-out;
      @include media(md) {
        min-height: unset;
      }

      &:hover {
        color: $mainGreen;
        text-decoration: underline;
      }
    }

    &__image {
      width: 20px;
      height: 20px;
    }


    &__text {
      font-size: 16px;
      padding-left: 16px;
      line-height: 1;
    }
  }

  &__scroll {
    margin-left: 16px;
    align-self: end;
    cursor: pointer;

    &:hover {
      svg {
        rect {
          fill: #acd8d1;
        }
      }
    }

    svg {
      rect {
        transition: 0.3s ease-in, 0.3s ease-in-out;
      }
    }

    @include media(md) {
      display: none;
    }
  }

  &__bottom {
    border-top: 1px solid #dfe2e6;
    padding: 32px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    @include media(md) {
      flex-direction: column;
    }

    &__left {
      @include media(md) {
        order: 2;
      }
    }

    &__right {
      @include media(md) {
        order: 1;
        margin-bottom: 36px;
      }
    }

    &__copyright {
      font-size: 14px;
      color: $grayTone6;
      @include media(xs) {
        text-align: center;
      }
    }

    &__list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      @include media(md) {
        justify-content: center;
      }
    }

    &__item {
      margin-right: 40px;
      @include media(xs) {
        margin: 0 20px;
      }
    }

    &__link {
      color: $grayTone6;
      font-size: 14px;
    }
  }

  &__scroll {
    min-width: 64px;
    width: 64px;
    height: 64px;
  }
}