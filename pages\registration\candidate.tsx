import globalStyle from "../../styles/Global.module.scss";
import AuthLayout from "../../component/Auth/AuthLayout";
import Image from "next/image";
import Link from "next/link";
import bg from "../../public/images/registration/figure1.svg";
import styles from "../../styles/CandidateRegistration.module.scss";
import {components} from "react-select";
import {selectCustomStyle} from "../../public/files/selectCustomStyle";
import {selectCustomErrorStyle} from "../../public/files/selectCustomErrorStyle";
import sbsPasswordCheckForErrors from "../../hook/sbsPasswordCheckForErrors";
import {validateEmail} from "../../hook/validateEmail";
import {validatePassword} from "../../hook/validatePassword";
import solidArrowDown from "../../public/images/icon/solid_arrow_down_ic.svg";
import {useEffect, useState} from "react";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import Head from "next/head";
import {industries, positions} from "../../interfaces/RegistrationPageInterfaces";
import {NextPage} from "next";
import axios from "axios";
import AsyncSelect from "react-select/async";
import {selectSearchFuncIndustry, selectSearchFuncPosition} from "../../hook/searchFunctions";

import hidePasswordIc from "../../public/images/icon/hide_password_ic.svg";
import displayPasswordIc from "../../public/images/icon/open_password_ic.svg";

const Option = (props: any) => {
	return (
		<div>
			<components.Option {...props} className={styles.registration__checkbox}>
				<input
					id={props.value}
					type="checkbox"
					checked={props.isSelected}
					onChange={() => null}
				/>{" "}
				<label htmlFor={props.value}><span/></label>
				<p>{props.label}</p>
			</components.Option>
		</div>
	);
};

interface Data {
	data: {
		Positions: Array<positions>,
		Industries: Array<industries>
	};
}

const Candidate: NextPage<Data> = ({data}) => {
	const [sbsPasswordValidation, setSbsPasswordValidation] = useState(false);
	const [disableButton, setDisableButton] = useState(false);
	const [firstNameError, setFirstNameError] = useState(false);
	const [lastNameError, setLastNameError] = useState(false);
	const [emailError, setEmailError] = useState(false);
	const [emailExistError, setEmailExistError] = useState(false);
	const [authError, setAuthError] = useState(false);
	const [phoneError, setPhoneError] = useState(false);
	const [passwordError, setPasswordError] = useState(false);
	const [confirmPasswordError, setConfirmPasswordError] = useState(false);
	const [industryError, setIndustryError] = useState(false);

	//display/hide password
	const [displayNewPassword, setDisplayNewPassword] = useState(false);
	const [displayConfirmPassword, setDisplayConfirmPassword] = useState(false);

	const [firstName, setFirstName] = useState("");
	const [middleName, setMiddleName] = useState("");
	const [lastName, setLastName] = useState("");
	const [email, setEmail] = useState("");
	const [phone, setPhone] = useState<any>(undefined);
	const [candidateType, setCandidate] = useState("");
	const [industry, setIndustry] = useState([]);
	const [position, setPosition] = useState<any>(null);
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [iAgree, setIAgree] = useState(false);
	const [iAgreeError, setIAgreeError] = useState(true);

	//validate password state
	const [minLength, setMinLength] = useState(false);
	const [oneNumber, setOneNumber] = useState(false);
	const [azSmallSymbol, setAzSmallSymbol] = useState(false);
	const [azBigSymbol, setAzBigSymbol] = useState(false);
	const [specialCharacters, setSpecialCharacters] = useState(false);

	useEffect(() => {
		sbsPasswordCheckForErrors(password, setMinLength, setOneNumber, setAzSmallSymbol, setAzBigSymbol, setSpecialCharacters);
	}, [password]);

	const formValidate = (): boolean => {
		return firstName.length !== 0 &&  lastName.length !== 0 && validateEmail(email) &&
			(phone !== undefined && isValidPhoneNumber(phone)) && validatePassword(password) && industry.length < 6
			&& password.length > 0 && confirmPassword.length > 0 && password === confirmPassword && iAgree;
	};

	const onSubmitFormHandler = async (e: any) => {
		e.preventDefault();
		firstName.length === 0 && (setFirstNameError(true));
		lastName.length === 0 && (setLastNameError(true));
		validateEmail(email) ? setEmailError(false) : setEmailError(true);
		industry.length > 5 ? setIndustryError(true) : setIndustryError(false);
		phone && isValidPhoneNumber(phone) ? setPhoneError(false) : setPhoneError(true);
		validatePassword(password) ? setPasswordError(false) : setPasswordError(true);
		password !== confirmPassword ? setConfirmPasswordError(true) : setConfirmPasswordError(false);
		if (!iAgree) setIAgreeError(false);

		if (formValidate()) {
			const formDataCandidate = {
				email: email,
				password: password,
				phone: phone,
				firstname: firstName,
				middlename: middleName,
				lastname: lastName,
				candidate_type: candidateType,
				industries: industry,
				positionId: position?.id,
			};
			await fetch(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/candidate`, {
				headers: {
					"content-type": "application/json",
				},
				method: "POST",
				body: JSON.stringify(formDataCandidate),
			}).then(function (res) {
				return res.json();
			}).then(async function (body) {
				if (body.statusCode) {
					setAuthError(body.message);
				} else {
					location.replace("/api/auth/login");
				}
			});
		}
	};

	const checkUserEmail = async () => {
		if (!emailError) {
			await axios.get(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/user/${email}`).then(() => {
				setEmailExistError(false);
			}).catch(() => {
				setEmailExistError(true);
			});
		}
	};

	useEffect(() => {
		if (formValidate() && !emailExistError && !industryError) {
			setDisableButton(true);
		} else {
			setDisableButton(false);
		}
	}, [firstName, middleName, lastName, email, phone, password, confirmPassword, iAgree, emailExistError, industryError]);

	return (
		<AuthLayout>
			<Head>
				<title>Registration Candidate | uRecruits</title>
			</Head>
			<section className={styles.registration}>
				<div className={styles.registration__inner}>
					<div className={styles.registration__left}>
						<div className={styles.registration__left__head}>
							<h2 className={styles.registration__headline}>
								Registration
							</h2>
							<div className={styles.registration__option}>
								<span className={styles.registration__option__line}/>
								<span className={styles.registration__option__main}>candidate</span>
								<div className={styles.registration__option__icon}>
									<Image src={solidArrowDown}/>
								</div>
								<ul className={styles.registration__option__list}>
									<li className={styles.registration__option__item}>
										<Link
											href={"/registration/company"}
										>
											<a className={styles.registration__option__link}>
												company
											</a>
										</Link>
									</li>
									<li className={styles.registration__option__item}>
										<Link
											href={"/registration/candidate"}
										>
											<a className={styles.registration__option__link}>
												candidate
											</a>
										</Link>
									</li>
								</ul>
							</div>
						</div>
						<form
							className={styles.registration__form}
							onSubmit={(e) => onSubmitFormHandler(e)}
						>
							<div
								className={`${styles.registration__form__item} ${firstNameError ? styles.error : ""} ${styles.short}`}>
								<label className={styles.registration__form__label}>
									Your First Name<span> *</span>
								</label>
								<input
									type="text"
									placeholder="Enter first name"
									className={styles.registration__form__input}
									value={firstName}
									name={firstName}
									onChange={(e) => {
										setFirstName(e.target.value);
										e.target.value.length === 0 ? setFirstNameError(true) : setFirstNameError(false);
									}}
								/>
								<p className={styles.errorMessage}>
									This is required field
								</p>
							</div>
							<div
								className={`${styles.registration__form__item}`}>
								<label className={styles.registration__form__label}>
									Your Middle Name
								</label>
								<input
									type="text"
									placeholder="Enter middle name"
									className={styles.registration__form__input}
									value={middleName}
									onChange={(e) => {
										setMiddleName(e.target.value);
									}}
								/>
							</div>
							<div
								className={`${styles.registration__form__item} ${lastNameError ? styles.error : ""}`}>
								<label className={styles.registration__form__label}>
									Your Last Name<span> *</span>
								</label>
								<input
									type="text"
									placeholder="Enter last name"
									className={styles.registration__form__input}
									value={lastName}
									onChange={(e) => {
										setLastName(e.target.value);
										e.target.value.length === 0 ? setLastNameError(true) : setLastNameError(false);
									}}
								/>
								<p className={styles.errorMessage}>
									This is required field
								</p>
							</div>
							<div className={`${styles.registration__form__item} ${emailError ? styles.error : ""}`}>
								<label className={styles.registration__form__label}>
									Email<span> *</span>
								</label>
								<input
									type="email"
									placeholder="Enter candidate email"
									className={styles.registration__form__input}
									value={email}
									onChange={(e) => {
										setEmail(e.target.value);
										validateEmail(e.target.value) ? setEmailError(false) : setEmailError(true);
										setEmailExistError(false);
									}}
									onBlur={() => {
										checkUserEmail();
									}}
								/>
								<p className={styles.errorMessage}>
									Please enter a valid Email address
								</p>
								{emailExistError && <p className={styles.existError}>This user already exist</p>}
							</div>
							<div className={`${styles.registration__form__item} ${phoneError ? styles.error : ""}`}>
								<label className={styles.registration__form__label}>
									Phone<span> *</span>
								</label>
								{/*@ts-ignore*/}
								<PhoneInput
									international
									defaultCountry="US"
									value={phone}
									onChange={(value: any) => {
										if (value) {

											setPhone(value);
											isValidPhoneNumber(value) ? setPhoneError(false) : setPhoneError(true);
										} else {
											setPhoneError(true);
										}
									}}
								/>
								<p className={styles.errorMessage}>
									Please enter a valid Phone number
								</p>
							</div>
							<div className={styles.registration__form__item}>
								<label className={styles.registration__form__label}>
									Candidate Type
								</label>
								<div className={styles.registration__form__group}>
									<div className={styles.registration__form__radio}>
										<input
											type="radio"
											id="fresher"
											name="candidateType"
											value="fresher"
											checked={candidateType === "fresher"}
											onChange={(e) => setCandidate(e.target.value)}
										/>
										<label htmlFor="fresher">Fresher</label>
									</div>
									<div className={styles.registration__form__radio}>
										<input
											type="radio"
											id="experienced"
											name="candidateType"
											value="experienced"
											checked={candidateType === "experienced"}
											onChange={(e) => setCandidate(e.target.value)}
										/>
										<label htmlFor="experienced">Experienced</label>
									</div>
								</div>
							</div>
							<div className={`${styles.registration__form__item} ${industryError ? styles.error : ""}`}>
								<label className={styles.registration__form__label}>
									Industry
								</label>
								<AsyncSelect
									cacheOptions
									loadOptions={(inputValue:any) => inputValue.length > 0 ? selectSearchFuncIndustry(inputValue) : selectSearchFuncIndustry("")}
									isMulti
									defaultOptions
									closeMenuOnSelect={industry.length > 4}
									hideSelectedOptions={false}
									value={industry}
									onChange={(option: any) => {
										setIndustry(option);
										option.length > 5 ? setIndustryError(true) : setIndustryError(false);
									}}
									components={{
										Option,
									}}
									styles={industryError ? selectCustomErrorStyle : selectCustomStyle}
									placeholder={"Select industry(ies)"}
									id="industry-select"
									instanceId="industry-select"
								/>
								<p className={styles.errorMessage}>
									You cannot select more 5 industry
								</p>
							</div>
							<div className={styles.registration__form__item}>
								<label className={styles.registration__form__label}>
									Position
								</label>
								<AsyncSelect
									cacheOptions
									loadOptions={(inputValue:any) => inputValue.length > 0 ? selectSearchFuncPosition(inputValue) : selectSearchFuncPosition("")}
									defaultOptions
									closeMenuOnSelect={false}
									hideSelectedOptions={true}
									value={position}
									onChange={(option: any) => {
										setPosition(option);
									}}
									placeholder={"Select your position"}
									styles={selectCustomStyle}
									id="positionSelect"
									instanceId="positionSelect"
								/>
							</div>
							<div
								className={`${styles.registration__form__item} ${passwordError ? styles.passwordError : ""} ${confirmPasswordError
									? styles.confirmPasswordError
									: ""}`}>
								<div className={styles.registration__form__container}>
									<label className={styles.registration__form__label}>
										Password<span> *</span>
									</label>
									<div className={styles.registration__validate}>
                          <span
	                          className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""} ${minLength ? styles.active : ""}`}>
                            8 min,
                          </span>
										<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${oneNumber ? styles.active : ""}`}>
                            1 num,
                          </span>
										<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${azSmallSymbol ? styles.active : ""}`}>
                             a-z,
                          </span>
										<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${azBigSymbol ? styles.active : ""}`}>
                            A-Z,
                          </span>
										<span className={`${styles.registration__validate__item} ${sbsPasswordValidation ? styles.focus : ""}
                             ${specialCharacters ? styles.active : ""}`}>
                            special characters
                          </span>
									</div>
								</div>
								<div className={styles.passwordInner}>
									<input
										type={displayNewPassword ? "text" : "password"}
										autoComplete="new-password"
										placeholder="Enter password"
										className={`${styles.registration__form__input} ${styles.password}`}
										value={password}
										onChange={(e) => {
											setPassword(e.target.value);
											validatePassword(e.target.value) ? setPasswordError(false) : setPasswordError(true);
											setSbsPasswordValidation(true);
										}}
									/>
									{
										!displayNewPassword ? (
											<img
												src={hidePasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayNewPassword(true)}
											/>
										) : (
											<img
												src={displayPasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayNewPassword(false)}
											/>
										)
									}
								</div>
								<div className={styles.passwordInner}>
									<input
										type={displayConfirmPassword ? "text" : "password"}
										autoComplete="new-password"
										placeholder="Confirm password"
										className={`${styles.registration__form__input} ${styles.confirm} ${styles.confirmPassword}`}
										value={confirmPassword}
										onChange={(e) => {
											setConfirmPassword(e.target.value);
											password !== e.target.value ? setConfirmPasswordError(true) : setConfirmPasswordError(false);
										}}
									/>
									{
										!displayConfirmPassword ? (
											<img
												src={hidePasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayConfirmPassword(true)}
											/>
										) : (
											<img
												src={displayPasswordIc.src}
												alt="icon"
												className={styles.passwordType}
												onClick={() => setDisplayConfirmPassword(false)}
											/>
										)
									}
								</div>
								<p className={styles.errorMessage}>
									{
										confirmPasswordError && ("Fields must be match.")
									}
									{
										passwordError && (" Password does not match validation.")
									}
								</p>
							</div>
							<div className={`${styles.registration__form__item} ${styles.iAgree} ${!iAgreeError ? styles.error : ""}`}>
								<div className={`${styles.registration__form__container} ${styles.rights}`}>
									<div className={styles.registration__form__checkbox}>
										<input
											type="checkbox"
											id="confirmCheckbox"
											checked={iAgree}
											onChange={() => {
												setIAgree(!iAgree);
												setIAgreeError(!iAgree);
											}}
										/>
										<label htmlFor="confirmCheckbox"><span/></label>
									</div>
									<p className={styles.registration__form__text}
									   onClick={() => {
										   setIAgree(!iAgree);
										   setIAgreeError(!iAgree);
									   }}>
										I agree with the
										<Link href={"/terms-of-service"}>
											<a className={styles.registration__form__link}> Terms of Service </a>
										</Link>
										and
										<Link href={"/privacy-policy"}>
											<a className={styles.registration__form__link}> Privacy Policy </a>
										</Link>
									</p>
								</div>
								<p className={styles.errorMessage}>
									You must accept the privacy terms
								</p>
							</div>
							<div className={`${styles.registration__form__item} ${styles.buttons}`}>
								<button
									className={`${styles.registration__form__submit}  ${globalStyle.filledButton} ${disableButton ? "" : globalStyle.disableFilledButton} `}>
									Create account
								</button>
								{authError && <p className={styles.authError}>{authError}</p>}
							</div>
						</form>
					</div>
					<div className={styles.registration__right}>
						<Image src={bg}/>
					</div>
				</div>
			</section>
		</AuthLayout>
	);
};

Candidate.getInitialProps = async () => {
	const response = await fetch("https://cms-dev.urecruits.com/industries-and-positions");
	const data = await response.json();
	return {data};
};

export default Candidate;