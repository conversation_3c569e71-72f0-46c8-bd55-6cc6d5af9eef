import SupportLayout from "../../../component/SupportLayout";
import { HelpCenterInterface } from "../../../interfaces/HomePageInterfaces";
import { NextPage } from "next";
import GivingBackToCommunitiesList from "../../../component/GivingBackToCommunities/GivingBackToCommunitiesList";

interface Data {
  bannerData: HelpCenterInterface;
  givingBackToCommunitiesData: any;
}

const GivingBackToCommunities: NextPage<Data> = ({ bannerData, givingBackToCommunitiesData }) => {
  return (
    <SupportLayout bannerData={bannerData.Banner}>
      {givingBackToCommunitiesData.map((item: any) => {
        return <GivingBackToCommunitiesList props={item}></GivingBackToCommunitiesList>;
      })}
    </SupportLayout>
  );
};

export default GivingBackToCommunities;

export async function getServerSideProps(context: { res: any; req: any; query: any }) {
  const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
  const givingBackToCommunitiesPageRes = await fetch("https://cms-dev.urecruits.com/giving-back-to-communities-lists");

  return {
    props: {
      bannerData: await helpCenterPageRes.json(),
      givingBackToCommunitiesData: await givingBackToCommunitiesPageRes.json(),
    },
  };
}
