@import "mixins";
@import "config";

.container {
  padding: 24px 32px;
  background: #FFFFFF;
  border: 1px solid #DFE2E6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 44px;
  color: $grayTone5;

  &:last-child {
    margin-bottom: 0;
  }

  &__icon {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-right: 60px;
    @include media(xs) {
      display: none;
    }
  }

  &__inner {
    width: calc(100% - 120px);
    display: flex;
    flex-direction: column;
    @include media(xs) {
      width: 100%;
    }
  }
}

.content_box {
  &__bottom_block {
    display: flex;
  }

  &__title {
    margin-bottom: 17px;
  }

  &__desc {
    margin-bottom: 24px;
  }

  &__text {
    font-size: 18px;
  }

  &__bottom_block {
    display: flex;
    align-items: center;

    svg {
      width: 40px;
      height: 40px;
      margin-right: 16px;
    }
  }
}

.information_block {
  display: flex;
  flex-direction: column;
  width: calc(100% - 56px);

  &__text {
    margin-bottom: 6px;
    font-size: 14px;

    &.bold, span {
      font-weight: 900;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.sub_categories {
  &__title {
    margin-bottom: 32px;
    font-size: 20px;
  }

  &__link {
    color: $grayTone5;
    padding: 24px 32px;
    background: #FFFFFF;
    border: 1px solid #DFE2E6;
    border-radius: 12px;
    cursor: pointer;
    margin-bottom: 36px;
    display: flex;
    flex-direction: column;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.breadcrumb {
  font-weight: 500;
  font-size: 14px;
  line-height: 140%;
  color: #999EA5;
  display: flex;
  align-items: center;
  margin-bottom: 32px;

  a, p {
    font-weight: 500;
    font-size: 14px;
    line-height: 140%;
    color: #999EA5;
  }

  p {
    margin: 0 4px;
  }
}

.list {
  display: flex;
  flex-direction: column;

}

.editor{
  h1, h2, h3, h4, h5, h6 {
    margin-bottom: 25px;
  }

  table {
    margin-bottom: 20px;
    display: block;
    overflow-x: auto;
    white-space: pre-wrap;

    tr {
      th {
        font-weight: 900;
      }

      td, th {
        border: 1px solid $grayTone3;
        padding: 10px;
        min-width: 200px;

        span {
          margin-bottom: 5px;
        }
      }
    }
  }

  p {
    margin-bottom: 20px;

    i {
      font-style: italic;
      width: 100%;

      span {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }

  span {
    margin-top: -10px;
    margin-bottom: 20px;
  }
  a{
    color: $grayTone5;
    font-weight: 800;
    transition: .3s ease-in,.3s ease-in-out;
    &:hover{
      color: $mainGreen;

    }
  }
  em {
    display: flex;
    flex-direction: column;

    span {
      margin-bottom: 10px;
    }
  }

  ul {
    margin-bottom: 20px;

    li {
      padding-left: 20px;
      list-style: disc inside;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  img{
    width: 100%;
    object-fit: contain;
    margin: 25px 0;
  }
}