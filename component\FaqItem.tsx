import styles from '../styles/Faq.module.scss'
import { useState } from 'react'
import {FaqItem} from "../interfaces/HomePageInterfaces";

type IProps = {
  item: FaqItem
}
const FaqItem = ({item}:IProps) => {
  const [open, setOpen] = useState(false)

  return(
    <li className={`${styles.faq__item} ${open ? styles.active: ''}`} >
      <div className={styles.faq__item__head} onClick={() => setOpen((prev) => !prev)}>
        <p className={styles.faq__item__name}>
          {item.title}
        </p>
        <div
          className={styles.faq__item__close}

        >
          <svg width="22" height="11" viewBox="0 0 22 11" fill="none">
            <path d="M1.25 1.25L11 9.5L20.75 1.25" stroke="#999EA5" strokeWidth="1.33333" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      {
        open && (
          <p className={styles.faq__item__detail}>
            {item.description}
          </p>
        )
      }
    </li>
  )
}

export default FaqItem