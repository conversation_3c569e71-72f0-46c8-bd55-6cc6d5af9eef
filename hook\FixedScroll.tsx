const fixedScroll = async (
  scrollRef: HTMLDivElement | null,
  nextElementRef: HTMLDivElement | null,
  margin: number
): Promise<boolean> => {
  let result:boolean=true
  if (scrollRef !== null) {
    if (window.scrollY > scrollRef.offsetTop && scrollRef.offsetTop > margin) {
      //checking if current position of scrollY more than position of block that needed to make sticky, scrollRef are component that needed to make sticky.
      //scrollRef.offsetTop > margin Used to avoid broking of logic when block being sticky and his offsetTop is equal padding-top\margin-top
      result=true
    } else {
      if (nextElementRef !== null) {
        if (window.scrollY < nextElementRef.offsetTop) {
          //removing styles if sticky block should have normal position in document,
          // nextElementRef is element that a going right after sticky block
          result=false
        }
      }
    }
  }


  return result
}
export default fixedScroll