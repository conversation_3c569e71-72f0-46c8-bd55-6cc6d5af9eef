import icon1 from '../public/images/jobs/icon1.png'

export interface bannerInterface {
  id: number,
  image: string,
  information: string,
  informationImage: string,
  tagline: string,
  headline: string,
  description: string,
  banner_image: any,
  get_started_text: string,
  get_started_link: string,
  try_demo_text: string
}

export interface WhyChooseUs {
  choose_item: Array<WhyChooseUsItem>,
  headline: string,
  tagline: string,
  id: number
}

export interface WhyChooseUsItem {
  description: string,
  headline: string,
  id: number,
  image: any,
  link_text: string,
  link_url: string
}

export interface AboutUsInterface {
  id: number,
  tagline: string,
  headline: string,
  description: string,
  image: any,
  aboutItem: Array<AboutUsItem>
}

export interface AboutUsItem {
  value: string,
  text: string,
}

export interface TryToBelieveInterface {
  description: string,
  headline: string,
  id: number,
  image: any,
  tagline: string,
  button_text: string
}

export interface ReviewsInterface {
  headline: string,
  id: number,
  tagline: string,
  reviewItem: Array<ReviewItem>
}

export interface ReviewItem {
  headline: string,
  description: string,
  author_name: string,
  author_position: string,
  author_image: any
}

export interface ServicesInterface {
  id: number,
  tagline: string,
  headline: string,
  description: string,
  button_text: string,
  services_item: Array<ServicesItem>
}

export interface ServicesItem {
  image: any,
  headline: string,
  description: string,
  link_text: string
  link_url: string
}

export interface FaqInterface {
  tagline: string,
  headline: string,
  tagline_after: string,
  faqitem: Array<FaqItem>
}

export interface FaqSupportInterface {
  contact: {
    title: string,
    phone: string,
    email: string,
    desc: string,
  },
  supportForm: {
    buttonCta: string
    emailLabel: string
    emailPlaceholder: string
    nameLabel: string
    namePlaceholder: string
    questionLabel: string
    questionPlaceholder: string
    title: string
  }
}

export interface FaqItem {
  title: string,
  description: string
}

export interface PlanInterface {
  name: string,
  id: number
  Tier: Array<TierInterface>,
  compare: Array<CompareInterface>,
  tagline_landing_page: string,
  headline_landing_page: string
}

export interface TierInterface {
  price_yearly: string,
  name: string,
  trial: string,
  price_monthly: string,
  pros_1: string,
  pros_2: string,
  pros_3: string,
  button_text: string,
  pros_4: string,
  pros_5: string,
  pros_6: string,
  link_landing_page: string
}

export interface CompareInterface {
  title: string,
  proses: Array<ProsesInterface>
}

export interface ProsesInterface {
  name: string,
  tier1_value: string,
  tier2_value: string,
  tier3_value: string,
  tier1_image: any,
  tier2_image: any,
  tier3_image: any,
}

export interface PricingInterface {
  tagline: string,
  headline: string,
  description: string,
  custom_plan: {
    headline_middle: string,
    headline_last: string,
    headline_first: string,
    tagline: string,
    text_right: string,
    button_right: string,
    button_left: string,
    text_left: string,
    description: string
  }
}

export interface ServicesPageInterface {
  Banner: {
    tagline: string,
    description: string,
    headline: string,
    button_left: string,
    button_right: string
  },
  try_for_free: {
    tagline: string,
    headline_first_black: string,
    headline_last_green: string,
    button_left: string,
    text_left: string,
    button_right: string,
    text_right: string,
    description: string,
    description_bold: string,
  }
  step: Array<ServicesStepInterface>,
}

export interface IAllJobs {
  count: number,
  rows: Array<IJobItem>
}

export interface IJobItem {
  id: number,
  aboutCompany: string,
  company: {
    id:number,
    avatar: null | string,
    name: string
  },
  locations:Array<{city:string,state:string,id:number}>,
  description: string,
  shortDescription: string,
  skills: Array<string>,
  title: string,
  remoteLocation: boolean,
  salaryMonthMax: number,
  salaryMonthMin: number,
  salaryYearMax: number,
  salaryYearMin: number,
  experienceMin: number,
  experienceMax: number,
  subscribes: Array<{
    applyJob: boolean,
    match: number,
    saveJob: boolean,
    subscribeJob: boolean,
  }>,
  applicationForm: Array<any>,
  match_percentage?:number
}

export interface ServicesPageTopInterfaces {
  Banner: ServicesPageBannerInterface,
  row: Array<ServicesPageRowInterface>
}

export interface ServicesPageRowInterface {
  tagline: string,
  headline: string,
  Features: Array<ServicesPageFeatureInterface>
}

export interface ServicesPageFeatureInterface {
  description: string,
  image: any,
  name: string,
  id: number
}

export interface ServicesPageBannerInterface {
  button_left: string,
  button_right: string,
  description: string,
  headline: string,
  tagline: string,
  image: any,
}

export interface ServicesStepInterface {
  headline: string,
  id: number,
  tagline: string,
  sub_step: Array<ServicesSubStepInterface>
}

export interface ServicesSubStepInterface {
  description: string,
  id: number,
  image: any,
  title: string
}

export interface HelpCenterInterface {
  Banner: {
    tagline: string,
    headline: string,
    description: string
  }
}

export interface IntegrationItemInterface {
  item: IntegrationObject
}

interface IntegrationObject {
  status: string,
  imageSrc: {
    height: number,
    src: string,
    width: number
  },
  name: string,
  tagline: string
  description: string
}

export interface JobCardInterface {
  data: IJobItem
  displayFormat: string,
  permissions: boolean,
  token: string,
  companyId?: number,
}

interface JobCardObject {
  id: number,
  logo: {
    height: number,
    src: string,
    width: number
  },
  title: string,
  companyName: string,
  companyLocation: string,
  tags: Array<string>,
  description: string,
  expMinRange: number,
  expMaxRange: number,
  salaryMinRange: number,
  salaryMaxRange: number
}
export interface IBreadCrumbs{
  name:string,
  link:string
}