import {store} from "../../store";
import styles from "../../styles/ApplyPopup.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import { changeSuccessApplyPopup} from "../../store/action-creator/app";
import successImage from "../../public/images/icon/popup_success_ic.svg";

const SuccessfullyApplyPopup = () => {

	return (
		<div className={`${styles.popup} ${styles.apply}`}>
			<div className={styles.popup__step}>
				<div className={styles.popup__head}>
					<p className={styles.popup__head__headline}>
					</p>
					<svg
						width="24"
						height="24"
						viewBox="0 0 24 24"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
						className={styles.popup__head__close}
						onClick={() => store.dispatch(changeSuccessApplyPopup(false))}
					>
						<path d="M18 6L12 12M6 18L12 12M12 12L6 6L18 18" stroke="#C1C5CB" strokeWidth="1.5"
						      strokeLinecap="round"
						      strokeLinejoin="round"/>
					</svg>
				</div>
				<div className={styles.popup__body}>
					<img src={successImage.src} alt="success icon" className={styles.popup__body__image}/>
					<p className={styles.popup__body__headline}>Successfully Applied</p>
				</div>
				<div className={`${styles.popup__bottom} ${styles.center}`}>
					<button
						className={`${styles.popup__bottom__button} ${globalStyle.filledButton}`}
						onClick={() => store.dispatch(changeSuccessApplyPopup(false))}
					>
						Got it!
					</button>
				</div>
			</div>
		</div>
	);
};
export default SuccessfullyApplyPopup;