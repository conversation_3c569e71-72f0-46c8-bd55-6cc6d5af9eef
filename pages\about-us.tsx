import type { NextPage } from 'next'
import Head from 'next/head'
import Layout from '../component/Layout'
import AboutBanner from '../component/About/AboutBanner'
import AboutResult from '../component/About/AboutResult'
import Timeline from '../component/About/Timeline'
import Vision from '../component/About/Vision'
import ContactUs from '../component/ContactUs'
import DemoPlan from '../component/About/DemoPlan'
import Happiness from '../component/About/Happiness'

interface Data {
  data: any;
}

const AboutPage: NextPage<Data> = ({ data }) => {
  return (
    <Layout>
      <Head>
        <title>About uRecruits | HR Tech Built for Smarter Hiring</title>
        <meta name="description" content="Learn about uRecruits our mission, team, and vision to transform HR through smart, data-driven recruitment technology. Discover who we are and why we care." />
      </Head>
      <AboutBanner props={data.banner}/>
      <AboutResult props={data.result}/>
      <Timeline props={data.Timeline}/>
      <Vision props={data.vision}/>
      <Happiness/>
      <DemoPlan props={data.demo}/>
      <ContactUs/>
    </Layout>
  )
};



export default AboutPage

AboutPage.getInitialProps = async () => {
  const response = await fetch('https://cms-dev.urecruits.com/about-us')
  const data = await response.json()
  return {data}
}
