import SupportLayout from "../../../../component/SupportLayout";
import {HelpCenterInterface, IBreadCrumbs} from "../../../../interfaces/HomePageInterfaces";
import {NextPage} from "next";
import ArticlesBoxContent from "../../../../component/Articles/ArticlesBoxContent";

import {memo} from "react";
import styles from '../../../../styles/articles-theme.module.scss'
import BreadCrumb from "../../../../component/BreadCrumb/BreadCrumb";
import ArticlesTheme from "../../../../component/Articles/ArticlesTheme";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

interface Data {
	bannerData: HelpCenterInterface,
	breadcrumbs: Array<IBreadCrumbs>,
	articlePost: any
}

const ArticlesPosts: NextPage<Data> = ({bannerData, breadcrumbs, articlePost}) => {
	console.log(articlePost)
	return (
		<SupportLayout bannerData={bannerData.Banner}>
			<BreadCrumb array={breadcrumbs}/>
			<div style={{
				background: 'white',
				cursor: 'unset',
				border: 'unset',
				marginBottom: '64px'
			}}>
				<ArticlesBoxContent title={articlePost.Title} titleFont={24} quantity={0} updatedAt={articlePost.updated_at}
				                    desc={articlePost.shortDescription}/>
			</div>
			<div className={styles.editor}>
				<ReactMarkdown rehypePlugins={[rehypeRaw]} components={{
					u: ({node, children, ...props}) => {
						return <u {...props} style={{textDecoration: "underline"}}>{children}</u>
					},
					a: ({node, children, ...props}) => {
						return <a {...props} target={'_blank'}>{children}</a>
					}
				}}>
					{articlePost.Editor}
				</ReactMarkdown>
			</div>
		</SupportLayout>
	);
};

export default memo(ArticlesPosts);

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
	const articlesPage = await fetch(`https://cms-dev.urecruits.com/articles-posts?slug=${context.query.post}`).then(res => res.json());
	if (articlesPage.length === 0) return {notFound: true}
	const subArticles = await fetch(`https://cms-dev.urecruits.com/sub-articles?slug=${articlesPage[0]?.sub_articles_theme?.slug}`).then(res => res.json());
	const breadcrumbsList = [
		{
			name: 'AllCollections',
			link: '/articles'
		},
		{
			name: subArticles[0]?.article?.Title,
			link: '/articles/' + subArticles[0]?.article?.slug
		},
		{
			name: articlesPage[0]?.Title,
			link: '/articles/posts/' + articlesPage[0]?.slug
		}
	]
	return {
		props: {
			articlePost: articlesPage[0],
			bannerData: await helpCenterPageRes.json(),
			breadcrumbs: breadcrumbsList
		},
	};
}

