@import "config";
@import "mixins";


.getStarted {
  background: $milkWhite url("../public/images/prices-custom-plan/bg.svg") center;
  background-size: cover;
  padding: unset;
  margin-top: 70px;
  @include media(xs) {
    margin-top: 50px;
  }

  &.demoPlan{
    .getStarted__description{
      max-width: 468px;
      line-height: 1.5;
    }
  }

  &__inner {
    @include container;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 64px 16px;
    @include media(xs) {
      padding: 32px;
    }
  }

  &__headline {
    color: $grayTone7;
    margin-bottom: 32px;
    font-size: 48px;
    @include media(xs) {
      margin-bottom: 16px;
    }

    span {
      color: $greenBlue1;
    }
  }

  &__description {
    line-height: 1;
    text-align: center;
    margin-bottom: 52px;
    font-family: 'Avenir LT Std', sans-serif;
    font-weight: 400;
    @include media(xs) {
      margin-bottom: 35px;
    }

    span {
      font-weight: 800;
    }

  }

  &__block {
    display: flex;
    justify-content: space-between;
    @include media(xss) {
      flex-wrap: wrap;
    }

    &__right {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 32px;
      @include media(xs) {
        margin-left: 8px;
      }

      @include media(xss) {
        margin-left: 0;
        width: 100%;
      }
    }

    &__left {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      margin-right: 32px;
      @include media(xs) {
        margin-right: 8px;
      }

      @include media(xss){
        margin-right: 0;
        width: 100%;
        margin-bottom: 16px;
      }
    }

    &__buttonFree {
      font-size: 24px;
      margin-bottom: 12px;

      @include media(xs) {
        font-size: 18px;
        margin-bottom: 12px;
      }

      @include media(xss) {
        font-size: 14px;
        width: 100%;
        text-align: center;
      }
    }

    &__buttonDemo {
      font-size: 24px;
      margin-bottom: 12px;
      @include media(xs) {
        font-size: 18px;
        margin-bottom: 12px;
      }

      @include media(xss) {
        font-size: 14px;
        width: 100%;
        text-align: center;
      }
    }

    &__description {
      max-width: 150px;
      text-align: center;
      font-size: 14px;
      color: $grayTone4;
      line-height: 1;
    }
  }
}