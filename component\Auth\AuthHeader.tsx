import styles from '../../styles/AuthLayout.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import logo from '../../public/images/icon/logo.svg'
import { useAction } from '../../hook/useAction'


const AuthHeader = () => {
  const { toggleMenu } = useAction()

  return(
    <div className={styles.header}>
      <div className={styles.header__inner}>
        <Link
          href={'/'}
        >
          <a className={styles.header__link}>
            <Image src={logo} alt={'header logo'}/>
          </a>
        </Link>
        <div
          className={styles.mobile}
          onClick={() => toggleMenu(true)}
        >
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  )
}

export default AuthHeader