import styles from "../../styles/JobFilter.module.scss";
import globalStyle from "../../styles/Global.module.scss";
import DatePicker from "react-datepicker";
import {default as ReactSelect} from "react-select";
import {components} from "react-select";
import {selectCustomStyle} from "../../public/files/selectCustomStyle";
import React, {useState} from "react";
import JobRange from "./JobRange";
import {NumericFormat} from "react-number-format";
import AsyncSelect from "react-select/async";
import {
	selectSearchFuncCompanies,
	selectSearchFuncIndustry,
	selectSearchFuncLocation,
	selectSearchFuncSkills,
} from "../../hook/searchFunctions";
import resetIcon from "../../public/images/icon/reset_ic.svg";
import rangeValidate from "../../hook/rangeValidate";
import {useTypedSelector} from "../../hook/useTypedSelector";
import {store} from "../../store";
import {changeFilter} from "../../store/action-creator/jobs";
import {useRouter} from "next/router";

const Option = (props: any) => {
	return (
		<div>
			<components.Option {...props} className={styles.filter__form__checkbox}>
				<input
					id={props.value}
					type="checkbox"
					checked={props.isSelected}
					onChange={() => null}
				/>{" "}
				<label htmlFor={props.value}><span/></label>
				<p>{props.label}</p>
			</components.Option>
		</div>
	);
};

const EXPERIENCE_YEAR_MIN = 1;
const EXPERIENCE_YEAR_MAX = 50;

const SALARY_YEAR_MIN = 1;
const SALARY_YEAR_MAX = 1000000000;

const SALARY_MONTH_MIN = 1;
const SALARY_MONTH_MAX = 20000000;

interface JobFilterI {
	displayFilter: boolean;
	activeTab?: number;
}


export function toJSONLocal(date: any) {
	let local = new Date(date);
	local.setMinutes(date.getMinutes() - date.getTimezoneOffset());
	return local.toJSON().slice(0, 10);
}

export function toJSONLocalPlusDay(date: any) {
	let local = new Date(date);
	local.setDate(local.getDate() + 1)

	local.setMinutes(date.getMinutes() - date.getTimezoneOffset());
	return local.toJSON().slice(0, 10);
}


const JobFilter = ({displayFilter,activeTab}: JobFilterI) => {
	const router = useRouter();
	const limit = useTypedSelector(state => state.jobs.limit);
	const initData = useTypedSelector(state => state.jobs.filters);

	//form data state
	const [jobTitle, setJobTitle] = useState(initData.jobTitle);
	const [jobLocation, setLocation] = useState(initData.jobLocation);
	const [postedOnData, setPostedOnData] = useState(initData.postedOnData);

	//salary data
	const [salaryMonthValue, setSalaryMonthValue] = useState(initData.salaryMonthValue);
	const [salaryMonthPrevData, setSalaryMonthPrevData] = useState(initData.salaryMonthValue);
	const [salaryMonthMinError, setSalaryMonthMinError] = useState(false);
	const [salaryMonthMaxError, setSalaryMonthMaxError] = useState(false);

	//salary year range
	const [salaryYearValue, setSalaryYearValue] = useState(initData.salaryYearValue);
	const [salaryYearPrevData, setSalaryYearPrevData] = useState(initData.salaryYearValue);
	const [salaryYearMinError, setSalaryYearMinError] = useState(false);
	const [salaryYearMaxError, setSalaryYearMaxError] = useState(false);

	//experience range
	const [experienceYearValue, setExperienceYearValue] = useState(initData.experienceYearValue);
	const [yearPrevData, setYearPrevData] = useState(initData.experienceYearValue);
	const [experienceYearMinError, setExperienceYearMinError] = useState(false);
	const [experienceYearMaxError, setExperienceYearMaxError] = useState(false);

	const [education, setEducation] = useState(initData.education);
	const [skills, setSkills] = useState(initData.skills);
	const [companyName, setCompanyName] = useState(initData.companyName);
	const [jobType, setJobType] = useState(initData.jobType);
	const [preferableShift, setPreferableShift] = useState(initData.preferableShift);
	const [industryType, setIndustryType] = useState(initData.industryType);
	const [functionalArea, setFunctionalArea] = useState(initData.functionalArea);


	const onSubmitFilterHandler = (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		store.dispatch(changeFilter(
			{
				jobTitle: jobTitle,
				jobLocation,
				postedOnData,
				salaryMonthValue,
				salaryYearValue,
				experienceYearValue,
				education,
				skills,
				companyName,
				jobType,
				preferableShift,
				industryType,
				functionalArea,
			},
		));

		router.push(decodeURI(`/jobs/?page=1
		&limit=${limit}
		&title=${jobTitle}
		${jobLocation ? jobLocation.map((x: any) => `&locations=${x.id}`).join("") : ""}
		&createdAtFrom=${postedOnData ? toJSONLocal(postedOnData) : ""}
		&createdAtTo=${postedOnData ? toJSONLocalPlusDay(postedOnData) : ""}
		&salaryMonthMin=${salaryMonthValue[0]}
		&salaryMonthMax=${salaryMonthValue[1]}
		&salaryYearMin=${salaryYearValue[0]}
		&salaryYearMax=${salaryYearValue[1]}
		&experienceMin=${experienceYearValue[0]}
		&experienceMax=${experienceYearValue[1]}
		&education=${education?.value ? education?.value : ""}
		${skills ? skills.map((x: any) => `&skills=${x.label}`).join("") : ""}
		&companyId=${companyName?.id ? companyName?.id : ""}
		&jobType=${jobType?.value ? jobType?.value : ""}
		&industryId=${industryType?.id ? industryType?.id : ""}
		&functionalArea=${functionalArea?.value ? functionalArea?.value : ""}
		&preferableShift=${preferableShift?.label ? preferableShift?.label : ""}
		&filterType=${activeTab==2?'matched':activeTab==3?'saved':activeTab==4?'applied':''}
		`));

	};

	const onResetFilterHandler = () => {
		setJobTitle("");
		setLocation(null);
		setPostedOnData(null);
		setSalaryMonthValue([1, 20000]);
		setSalaryYearValue([1, 1000000]);
		setExperienceYearValue([1, 50]);
		setEducation(null);
		setSkills(null);
		setCompanyName(null);
		setJobType(null);
		setPreferableShift(null);
		setIndustryType(null);
		setFunctionalArea(null);

		store.dispatch(changeFilter(
			{
				jobTitle: "",
				jobLocation: null,
				postedOnData: null,
				salaryMonthValue: [1000, 4000],
				salaryYearValue: [10000, 40000],
				experienceYearValue: [1, 5],
				education: null,
				skills: null,
				companyName: null,
				jobType: null,
				preferableShift: null,
				industryType: null,
				functionalArea: null
			},
		));

		router.push(decodeURI(`/jobs/?filterType=${activeTab==2?'matched':activeTab==3?'saved':activeTab==4?'applied':''}`));
	};

	return (
		<div className={`${styles.filter} ${displayFilter ? styles.active : ""}`}>
			<form
				className={styles.filter__form}
				onSubmit={(e) => onSubmitFilterHandler(e)}
			>
				<div className={styles.filter__form__item}>
					<div className={styles.filter__head}>
						<p className={styles.filter__head__text}>
							Search within jobs
						</p>
						<div
							className={styles.filter__head__clear}
							onClick={onResetFilterHandler}
						>
							clear
						</div>
					</div>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Job Title
					</label>
					<input
						type="text"
						className={styles.filter__form__input}
						placeholder="Enter job title"
						value={jobTitle}
						onChange={(e) => {
							e.preventDefault();
							setJobTitle(e.target.value);
						}}
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Job Location
					</label>
					<AsyncSelect
						cacheOptions
						loadOptions={(inputValue:string) => inputValue.length > 0 ? selectSearchFuncLocation(inputValue) : selectSearchFuncLocation("")}
						isMulti
						defaultOptions
						value={jobLocation}
						onChange={(option: any) => {
							setLocation(option);
						}}
						hideSelectedOptions={false}
						closeMenuOnSelect={false}
						components={{
							Option,
						}}
						styles={selectCustomStyle}
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Posted on
					</label>
					<div className={styles.filter__form__date}>
						{/*@ts-ignore*/}
						<DatePicker
							selected={postedOnData}
							showYearDropdown={true}
							scrollableYearDropdown={true}
							yearDropdownItemNumber={70}
							onChange={(date: any) => {
								setPostedOnData(date);
							}}
							dateFormat="yyyy-MM-dd"
							maxDate={new Date()}
							placeholderText="Select date of post"
						/>
					</div>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Salary Range<span> (per month)</span>
					</label>
					<div className={styles.filter__range}>
						<div className={styles.filter__range__head}>
							<div className={styles.filter__range__left}>
								<div className={`${styles.filter__range__inner} ${salaryMonthMinError ? styles.error : ""}`}>
									<span className={styles.filter__range__currency}>$</span>
									<NumericFormat
										value={salaryMonthValue[0]}
										onValueChange={(values: any) => {
											const validateResult = rangeValidate(values.floatValue, salaryMonthValue, SALARY_MONTH_MIN, SALARY_MONTH_MAX, "min");
											if (validateResult) {
												setSalaryMonthMinError(true);
											} else {
												setSalaryMonthMinError(false);
												setSalaryMonthPrevData((prev: any) => [values.floatValue, prev[1]]);
											}
											setSalaryMonthValue((prev: any) => [values.floatValue, prev[1]]);
										}}
										className={styles.filter__range__input}
										thousandSeparator=","
									/>
								</div>
							</div>
							<div className={styles.filter__range__right}>
								<div className={`${styles.filter__range__inner} ${salaryMonthMaxError ? styles.error : ""}`}>
									<span className={styles.filter__range__currency}>$</span>
									<NumericFormat
										value={salaryMonthValue[1]}
										onValueChange={(values: any) => {
											const validateResult = rangeValidate(values.floatValue, salaryMonthValue, SALARY_MONTH_MIN, SALARY_MONTH_MAX, "max");
											if (validateResult) {
												setSalaryMonthMaxError(true);
											} else {
												setSalaryMonthMaxError(false);
												setSalaryMonthPrevData((prev: any) => [prev[0], values.floatValue]);
											}
											setSalaryMonthValue((prev: any) => [prev[0], values.floatValue]);
										}}
										className={styles.filter__range__input}
										thousandSeparator=","
									/>
								</div>
							</div>
						</div>
						<div className={styles.filter__range__body}>
							<JobRange
								values={salaryMonthMaxError || salaryMonthMinError ? salaryMonthPrevData : salaryMonthValue}
								setPrevData={setSalaryMonthPrevData}
								setValues={setSalaryMonthValue}
								maxValue={SALARY_MONTH_MAX}
								minValue={SALARY_MONTH_MIN}
							/>
						</div>
					</div>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Salary Range<span> (per year)</span>
					</label>
					<div className={styles.filter__range}>
						<div className={styles.filter__range__head}>
							<div className={styles.filter__range__left}>
								<div className={`${styles.filter__range__inner} ${salaryYearMinError ? styles.error : ""}`}>
									<span className={styles.filter__range__currency}>$</span>
									<NumericFormat
										value={salaryYearValue[0]}
										onValueChange={(values: any) => {
											const validateResult = rangeValidate(values.floatValue, salaryYearValue, SALARY_YEAR_MIN, SALARY_YEAR_MAX, "min");
											if (validateResult) {
												setSalaryYearMinError(true);
											} else {
												setSalaryYearMinError(false);
												setSalaryYearPrevData((prev: any) => [values.floatValue, prev[1]]);
											}
											setSalaryYearValue((prev: any) => [values.floatValue, prev[1]]);
										}}
										className={styles.filter__range__input}
										thousandSeparator=","
									/>
								</div>
							</div>
							<div className={styles.filter__range__right}>
								<div className={`${styles.filter__range__inner} ${salaryYearMaxError ? styles.error : ""}`}>
									<span className={styles.filter__range__currency}>$</span>
									<NumericFormat
										value={salaryYearValue[1]}
										onValueChange={(values: any) => {
											const validateResult = rangeValidate(values.floatValue, salaryYearValue, SALARY_YEAR_MIN, SALARY_YEAR_MAX, "max");
											if (validateResult) {
												setSalaryYearMaxError(true);
											} else {
												setSalaryYearMaxError(false);
												setSalaryYearPrevData((prev: any) => [prev[0], values.floatValue]);
											}
											setSalaryYearValue((prev: any) => [prev[0], values.floatValue]);
										}}
										className={styles.filter__range__input}
										thousandSeparator=","
									/>
								</div>
							</div>
						</div>
						<div className={styles.filter__range__body}>
							<JobRange
								values={salaryYearMaxError || salaryYearMinError ? salaryYearPrevData : salaryYearValue}
								setValues={setSalaryYearValue}
								setPrevData={setSalaryYearPrevData}
								maxValue={SALARY_YEAR_MAX}
								minValue={SALARY_YEAR_MIN}
							/>
						</div>
					</div>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Experience<span> (years)</span>
					</label>
					<div className={styles.filter__range}>
						<div className={styles.filter__range__head}>
							<div className={styles.filter__range__left}>
								<div
									className={`${styles.filter__range__inner} ${styles.small} ${experienceYearMinError ? styles.error : ""}`}>
									<input
										type="text"
										className={`${styles.filter__range__input}`}
										value={experienceYearValue[0]}
										onChange={(e) => {
											const currentValue = e.target.value === "" ? 0 : parseInt(e.target.value);

											const validateResult = rangeValidate(currentValue, experienceYearValue, EXPERIENCE_YEAR_MIN, EXPERIENCE_YEAR_MAX, "min");

											if (validateResult) {
												setExperienceYearMinError(true);
											} else {
												setExperienceYearMinError(false);
												setYearPrevData((prev: any) => [currentValue, prev[1]]);
											}
											setExperienceYearValue((prev: any) => [currentValue, prev[1]]);
										}}
									/>
								</div>
							</div>
							<div className={styles.filter__range__right}>
								<div
									className={`${styles.filter__range__inner} ${styles.small} ${experienceYearMaxError ? styles.error : ""}`}>
									<input
										type="text"
										className={`${styles.filter__range__input}`}
										value={experienceYearValue[1]}
										onChange={(e) => {
											const currentValue = e.target.value === "" ? 0 : parseInt(e.target.value);

											if (rangeValidate(currentValue, experienceYearValue, EXPERIENCE_YEAR_MIN, EXPERIENCE_YEAR_MAX, "max")) {
												setExperienceYearMaxError(true);
											} else {
												setExperienceYearMaxError(false);
												setYearPrevData((prev: any) => [prev[0], currentValue]);
											}

											setExperienceYearValue((prev: any) => [prev[0], currentValue]);
										}}
									/>
								</div>
							</div>
						</div>
						<div className={styles.filter__range__body}>
							<JobRange
								values={experienceYearMaxError || experienceYearMinError ? yearPrevData : experienceYearValue}
								setValues={setExperienceYearValue}
								setPrevData={setYearPrevData}
								maxValue={EXPERIENCE_YEAR_MAX}
								minValue={EXPERIENCE_YEAR_MIN}
							/>
						</div>
					</div>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Education
					</label>
					<ReactSelect
						options={educationOption}
						hideSelectedOptions={false}
						onChange={(item: any) => {
							setEducation(item);
						}}
						value={education}
						styles={selectCustomStyle}
						placeholder={"Select education"}
						id="educationSelect"
						instanceId="educationSelect"
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Skills
					</label>
					<AsyncSelect
						cacheOptions
						loadOptions={(inputValue:string) => inputValue.length > 0 ? selectSearchFuncSkills(inputValue) : selectSearchFuncSkills("")}
						isMulti
						defaultOptions
						value={skills}
						onChange={(option: any) => {
							setSkills(option);
						}}
						hideSelectedOptions={false}
						closeMenuOnSelect={false}
						components={{
							Option,
						}}
						styles={selectCustomStyle}
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Company name
					</label>
					<AsyncSelect
						cacheOptions
						loadOptions={(inputValue:string) => inputValue.length > 0 ? selectSearchFuncCompanies(inputValue) : selectSearchFuncCompanies("")}
						defaultOptions
						closeMenuOnSelect={true}
						hideSelectedOptions={false}
						value={companyName}
						placeholder={`Select company`}
						onChange={(option: any) => {
							setCompanyName(option);
						}}
						id="company"
						instanceId="company"
						styles={selectCustomStyle}
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Job Type
					</label>
					<ReactSelect
						options={jobTypeOption}
						closeMenuOnSelect={true}
						hideSelectedOptions={false}
						onChange={(item: any) => {
							setJobType(item);
						}}
						value={jobType}
						styles={selectCustomStyle}
						placeholder={"Select job type"}
						id="jobType"
						instanceId="jobType"
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Preferable Shift
					</label>
					<ReactSelect
						options={preferableShiftOption}
						closeMenuOnSelect={true}
						hideSelectedOptions={false}
						onChange={(item: any) => {
							setPreferableShift(item);
						}}
						value={preferableShift}
						placeholder={"Select preferable shift"}
						styles={selectCustomStyle}
						id="preferableShiftSelect"
						instanceId="preferableShiftSelect"
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Industry Type
					</label>
					<AsyncSelect
						cacheOptions
						loadOptions={(inputValue:string) => inputValue.length > 0 ? selectSearchFuncIndustry(inputValue) : selectSearchFuncIndustry("")}
						defaultOptions
						closeMenuOnSelect={true}
						hideSelectedOptions={false}
						value={industryType}
						placeholder={`Select industry type`}
						onChange={(option: any) => {
							setIndustryType(option);
						}}
						id="industry"
						instanceId="industry"
						styles={selectCustomStyle}
					/>
				</div>
				<div className={styles.filter__form__item}>
					<label className={styles.filter__form__label}>
						Functional Area
					</label>
					<ReactSelect
						options={functionAreaOption}
						closeMenuOnSelect={true}
						hideSelectedOptions={false}
						onChange={(item: any) => {
							setFunctionalArea(item);
						}}
						value={functionalArea}
						placeholder={"Select functional area"}
						styles={selectCustomStyle}
						id="functionalAreaSelect"
						instanceId="functionalAreaSelect"
					/>
				</div>
				<div className={styles.filter__form__item}>
					<button className={`${styles.filter__form__button} ${globalStyle.filledButton}`} type="submit">
						Apply filters
					</button>
				</div>
				<div className={styles.filter__form__item}>
					<div className={styles.filter__form__reset} onClick={onResetFilterHandler}>
						<img src={resetIcon.src} alt=""/>
						Reset all filters
					</div>
				</div>
			</form>
		</div>
	);
};

export default JobFilter;

const educationOption = [
	{
		value: "High School",
		label: "High School",
	},
	{
		value: "Associate degree",
		label: "Associate degree",
	},
	{
		value: "Bachelor's degree",
		label: "Bachelor's degree",
	},
	{
		value: "Master's degree",
		label: "Master's degree",
	},
	{
		value: "Doctorate degree",
		label: "Doctorate degree",
	},
	{
		value: "Professional degree",
		label: "Professional degree",
	},
];
const jobTypeOption = [
	{value: "Full-Time Employees", label: "Full-Time Employees"},
	{value: "Part-Time Employees", label: "Part-Time Employees"},
	{value: "Temporary Employees", label: "Temporary Employees"},
	{value: "Seasonal Employees", label: "Seasonal Employees"},
	{value: "Independent Contractors", label: "Independent Contractors"},
	{value: "Freelancers", label: "Freelancers"},
	{value: "Temporary workers", label: "Temporary workers"},
	{value: "Consultants", label: "Consultants"},
];
const preferableShiftOption = [
	{value: "generalShift", label: "General Shift"},
	{value: "morningShift", label: "Morning Shift"},
	{value: "afternoonShift", label: "Afternoon Shift"},
	{value: "eveningShift", label: "Evening Shift"},
	{value: "nightShift", label: "Night Shift"},
	{value: "flexibleShift", label: "Flexible Shift"},
];
const functionAreaOption = [
	{value: "Strategy", label: "Strategy"},
	{value: "Marketing", label: "Marketing"},
	{value: "Finance", label: "Finance"},
	{value: "Human resources", label: "Human resources"},
	{value: "Technology and equipment", label: "Technology and equipment"},
	{value: "Operations", label: "Operations"},
];