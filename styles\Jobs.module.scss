@import "config";
@import "mixins";

.jobs {

  &__inner {
    @include container();
    @include media(xs) {
      padding-right: 16px;
      padding-left: 16px;
    }
  }

  &__head {

  }

  &__headline {
    margin-bottom: 20px;
  }

  &__action {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media(lg) {
      flex-direction: column;
      align-items: flex-start;
    }

    &__left {

    }

    &__list {
      display: flex;
      align-items: center;
      @include media(sm) {
        flex-wrap: wrap;
      }
    }

    &__item {
      padding: 7px 20px;
      border: 1px solid $grayTone2;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      cursor: pointer;
      transition: .3s ease-in, .3s ease-in-out;
      @include media(sm) {
        margin-bottom: 8px;
      }
      @include media(xss) {
        margin-right: 0;
        width: 100%;
        justify-content: flex-start;
      }

      &:hover {
        background: $lightGreen;
        border-color: $lightGreen;

        .jobs__action__name,
        {
          color: $greenBlue2;
        }

        .jobs__action__img path {
          stroke: $greenBlue2;
        }
      }

      &:last-child {
        margin-right: 0;
      }

      &.active {
        background: $lightGreen;
        border-color: $lightGreen;

        .jobs__action__name,
        {
          color: $greenBlue2;
        }

        .jobs__action__img path {
          stroke: $greenBlue2;
        }
      }
    }

    &__img {
      width: 20px;
      height: 20px;
      min-width: 20px;
      object-fit: contain;
      transition: .3s ease-in, .3s ease-in-out;
    }

    &__name {
      padding-left: 14px;
      font-size: 14px;
      line-height: 1;
      color: $grayTone7;
      transition: .3s ease-in, .3s ease-in-out;
    }

    &__right {
      display: flex;
      @include media(lg) {
        margin-top: 20px;
        align-self: end;
      }
      @include media(sm) {
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 16px;
      }
    }
  }

  &__filterBtn {
    display: none;
    align-items: center;
    cursor: pointer;
    @include media(sm) {
      display: flex;
    }

    &:hover {
      .jobs__filterBtn__text {
        color: $mainGreen;
      }
    }

    &__icon {
      min-width: 16px;
      width: 16px;
      height: 16px;
    }

    &__text {
      font-size: 14px;
      color: $grayTone4;
      padding-left: 4px;
      transition: .3s ease-in, .3s ease-in-out;
    }
  }

  &__show {
    display: flex;
    align-items: center;

    &__text {
      font-size: 12px;
    }

    &__select {
      margin: 0 12px;
    }
  }

  &__format {
    display: flex;
    align-items: center;
    margin-left: 12px;
    @include media(lg) {
      display: none;
    }

    &__item {
      margin-left: 20px;

      svg {
        cursor: pointer;
      }

      &.active {
        svg {
          path {
            stroke: $mainGreen;
          }
        }
      }
    }
  }

  &__body {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    @include media(sm) {
      flex-direction: column;
    }

    &__left {
      max-width: 308px;
      width: 100%;
      @include media(sm) {
        max-width: 100%;
        margin-bottom: 32px;
      }
    }

    &__right {
      width: calc(100% - 308px);
      padding-left: 24px;
      @include media(sm) {
        width: 100%;
        padding-left: 0;
      }
    }

  }

  &__count {
    font-size: 16px;
    margin-bottom: 12px;
  }

  &__list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &__pagination{
    display: flex;
    align-items: center;
    justify-content: center;
  }
}