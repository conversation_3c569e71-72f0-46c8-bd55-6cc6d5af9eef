@import "mixins";
@import "config";

.banner {
  padding-bottom: 328px;
  padding-top: 216px;
  background: $milkWhite url("../public/images/about/bg.svg") center;
  margin-top: -122px;
  background-size: cover;

  @include media(xs) {
    padding-bottom: 100px;
    padding-top: 136px;
    margin-bottom: -96px;
  }

  &__inner {
    @include container();
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__content {
    margin: 0 auto;
    max-width: 540px;
  }

  &__tagline {
    text-align: center;
  }

  &__headline {
    text-align: center;
    margin-bottom: 28px;

    span {
      color: $mainGreen;
    }
  }

  &__description {
    text-align: center;
  }
}