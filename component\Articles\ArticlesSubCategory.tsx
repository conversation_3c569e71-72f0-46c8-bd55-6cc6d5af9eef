import {memo} from "react";
import styles from '../../styles/articles-theme.module.scss'
import ArticlesBoxContent from "../../component/Articles/ArticlesBoxContent";
import Link from "next/link";
export default memo(({item}:{item:any})=>{
	return <div className={styles.sub_categories}>
		<h3 className={styles.sub_categories__title}>{item.title}</h3>
		{
			item.articles_posts.map((value:any,index:any)=>{
				return <Link href={`/articles/posts/${value.slug}`} key={index}>
					<a className={styles.sub_categories__link}>
						<ArticlesBoxContent title={value.Title} titleFont={18} desc={value.shortDescription} updatedAt={value.updated_at} quantity={1}/>
					</a>
				</Link>
			})
		}
	</div>
})