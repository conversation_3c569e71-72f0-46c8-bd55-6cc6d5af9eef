import { handleAuth,  handleLogin } from '@auth0/nextjs-auth0';

export default handleAuth({
	async login(req, res) {
		try {
			const { redirectUri }: { redirectUri?: string } = req.query;
			const validateUri = redirectUri && redirectUri.startsWith('/job') && redirectUri.endsWith('apply')
			const returnTo = validateUri ? `${process.env.RETURN_TO}${redirectUri}` : process.env.RETURN_TO;

			await handleLogin(req, res, {
				returnTo,
				authorizationParams: {
					audience: "https://api-authz.urecruits.com/",
				},
			});
		} catch (error: any) {
			console.error('Login error:', error);
			res.status(error.status || 400).end(error.message);
		}
	}
});