@import "config";
@import "mixins";

.header {
  background: rgba(255, 255, 255, 0);

  &__inner {
    @include container;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 16px;
  }

  &__link {
    display: block;
    max-width: 150px;
    width: 100%;
    height: 32px;
  }

  &__nav {
    @include media(lg) {
      display: none;
    }
  }

  &__list {
    display: flex;
    margin: 0 32px;

    &__item {
      margin-right: 48px;
      display: flex;
      align-items: center;
      cursor: pointer;
      position: relative;

      @include media(xl) {
        margin-right: 30px;
      }

      &:hover {
        &:after {
          content: "";
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 12px;
          border-bottom: 2px solid $mainGreen;
        }

        .header__submenu {
          display: block;
        }

        .header__list__arrow {
          transform: rotate(180deg);
        }
      }

      &.active {
        &:before {
          content: "";
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 12px;
          border-bottom: 2px solid $mainGreen;
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }

    &__item.services {
      &:hover {
        &:after {
          display: none;
        }
      }
    }

    &__arrow {
      margin-left: 4px;
      transition: 0.3s ease-in, 0.3s ease-in-out;
    }

    &__link,
    &__text {
      color: $black;
      font-weight: 500;
      line-height: 1;
      padding: 20px 0;
    }
  }

  .buttons {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    @include media(lg) {
      display: none;
    }

    &__login {
      margin-right: 32px;
      color: $black;
      font-weight: 500;
      position: relative;
      display: block;

      &:hover {
        &:after {
          content: "";
          position: absolute;
          width: 100%;
          left: 0;
          bottom: -3px;
          border-bottom: 2px solid $mainGreen;
        }
      }
    }
  }

  &__submenu {
    display: none;
    top: 100%;
    position: absolute;
    padding: 28px;
    background: $white;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    width: 308px;
    z-index: 10;
    border-radius: 3px;

    &:before {
      content: "";
      display: block;
      width: 18px;
      height: 18px;
      transform: rotate(45deg) translate(-29px, -23px);
      background: #ffffff;
    }

    &__item {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 26px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__item.first {
      .header__submenu__link {
        font-size: 20px;
        color: $mainGreen;
        font-weight: 500;
      }
    }

    &__link {
      color: $grayTone7;
      font-weight: 500;
      display: flex;
      align-items: center;
      line-height: 1;
      width: 100%;
      transition: 0.3s ease-in, 0.3s ease-in-out;

      &:hover {
        color: $mainGreen;
      }
    }

    &__description {
      font-size: 12px;
      color: $grayTone4;
      margin-top: 8px;
      cursor: default;
    }

    &__icon {
      margin-right: 16px;
    }
  }

  .mobile {
    width: 16px;
    height: 16px;
    position: relative;
    cursor: pointer;
    display: none;
    @include media(lg) {
      display: block;
    }

    span {
      position: absolute;
      border-radius: 2px;
      transition: 0.3s cubic-bezier(0.8, 0.5, 0.2, 1.4);
      width: 100%;
      height: 2px;
      transition-duration: 500ms;
      background: $grayTone5;

      &:nth-child(1) {
        top: 0;
        right: 0;
        width: 100%;
      }

      &:nth-child(2) {
        top: 7px;
        right: 0;
        opacity: 1;
        width: 90%;
      }

      &:nth-child(3) {
        bottom: 0;
        right: 0;
        width: 75%;
      }
    }
  }

  &__info {
    background: $white;
    padding: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    &__text {
      font-size: 16px;
      font-weight: 400;
      color: $grayTone7;
      line-height: 1.5;
      background: $white;
    }

    &__link {
      color: $mainGreen;
      font-weight: 800;
      line-height: 1.5;
      cursor: pointer;
    }
  }

  .successContainer {
    display: flex;
    align-items: center;
    flex-shrink: 0;
   
    @include media(lg) {
    margin-left: auto;
    }
  }

  .appLink {
    color: $mainGreen;
    font-weight: 600;
    line-height: 1.5;
    transition: 0.3s ease-in, 0.3s ease-in-out;
    margin-right: 32px;
    margin-left: auto;

    @include media(md) {
      display: none;
    }

    &:hover {
      text-decoration: underline;
    }
  }
}
