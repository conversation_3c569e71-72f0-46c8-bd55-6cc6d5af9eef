import SupportLayout from "../../../component/SupportLayout";
import {HelpCenterInterface, IBreadCrumbs} from "../../../interfaces/HomePageInterfaces";
import {NextPage} from "next";
import ArticlesTheme from "../../../component/Articles/ArticlesTheme";
import {memo} from "react";
import styles from '../../../styles/articles-theme.module.scss'
import BreadCrumb from "../../../component/BreadCrumb/BreadCrumb";
import ArticlesSubCategory from "../../../component/Articles/ArticlesSubCategory";


interface Data {
	bannerData: HelpCenterInterface,
	articlesTheme: any,
	breadcrumbs: Array<IBreadCrumbs>
}

const Articles: NextPage<Data> = ({bannerData, articlesTheme, breadcrumbs}) => {
	return (
		<SupportLayout bannerData={bannerData.Banner}>
			{
				articlesTheme.length > 0 ?
					<div className={styles.list}>
						<BreadCrumb array={breadcrumbs}/>
						<ArticlesTheme item={articlesTheme[0].article} styleProp={{
							background: '#F5FCFF',
							cursor: 'unset',
							border: '1px solid #F5FCFF',
							marginBottom: '64px'
						}} titleFont={24} quantity={articlesTheme.length}/>
						{
							articlesTheme.map((item:any,index:number)=>{
								return item.articles_posts.length>0&&<ArticlesSubCategory item={item} key={index}/>
							})
						}
					</div>
					:
					<p>There no articles yet</p>
			}
		</SupportLayout>
	);
};

export default memo(Articles);

export async function getServerSideProps(context: { res: any, req: any, query: any }) {
	const helpCenterPageRes = await fetch("https://cms-dev.urecruits.com/help-center-page");
	const articlesPage = await fetch(`https://cms-dev.urecruits.com/sub-articles?article.slug=${context.query.theme}`).then(res => res.json());
	if(articlesPage.length===0) return {
			notFound:true
	}
	const breadcrumbsList = [
		{
			name: 'AllCollections',
			link: '/articles'
		},
		{
			name: articlesPage[0]?.title,
			link: '/articles/' + articlesPage[0]?.article?.slug
		}
	]
	return {
		props: {
			articlesTheme: articlesPage,
			bannerData: await helpCenterPageRes.json(),
			breadcrumbs: breadcrumbsList
		},
	};
}

