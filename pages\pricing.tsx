import Layout from '../component/Layout'
import PricesBanner from '../component/PricesBanner'
import Faq from '../component/Faq'
import ContactUs from '../component/ContactUs'
import PriceTable from '../component/PriceTable'
import CustomPlan from '../component/CustomPlan'
import Head from "next/head";
import {NextPage} from "next";
import {
  FaqInterface, PlanInterface, PricingInterface,
} from "../interfaces/HomePageInterfaces";

interface Data {
  faq: FaqInterface,
  plan: Array<PlanInterface>,
  pricing: PricingInterface
}

const Pricing: NextPage<Data> = ({faq, plan, pricing}) => {
  return (
    <>
      <Head>
        <title>uRecruits Pricing Plans | Assessment & Hiring Packages</title>
        <meta name="description" content="Explore URecruits' flexible pricing for hiring, assessments, and HR support. Choose from Basic, Plus, Pro, or custom packages—30-day free trial included." />
      </Head>
      <Layout>
        <PricesBanner props={pricing}/>
        <PriceTable props={plan}/>
        <CustomPlan props={pricing}/>
        <Faq props={faq}/>
        <ContactUs/>
      </Layout>
    </>
  )
}

Pricing.getInitialProps = async () => {
  const response_faq = await fetch('https://cms-dev.urecruits.com/faq')
  const response_pricing = await fetch('https://cms-dev.urecruits.com/pricing')
  const response_plan = await fetch('https://cms-dev.urecruits.com/plans')
  const faq = await response_faq.json()
  const plan = await response_plan.json()
  const pricing = await response_pricing.json()
  return {faq, plan, pricing}
}

export default Pricing
