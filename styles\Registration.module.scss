@import "mixins";
@import "config";

.registration {
  padding: 0;
  width: 100%;
  min-height: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  @include media(xs) {
    justify-content: flex-start;
  }

  &__inner {
    @include authContainer;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__left {
    max-width: 432px;
    width: 100%;
    padding-right: 32px;
    @include media(md) {
      padding-right: 0;
      max-width: 100%;
    }
  }

  &__tagline {
  }

  &__headline {
    margin-bottom: 40px;
    font-weight: 500;
  }

  &__role {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 30px;

    &__image {
      width: 150px;
      min-width: 150px;
      height: 158px;
      display: block;
      @include media(xss){
        width: 120px;
        min-width: 120px;
        height: 128px;
      }

      &:hover + .registration__role__link{
        border-color: #099FE3;
        border-left-color: $grayTone2;
      }
    }

    &__link {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      font-size: 16px;
      color: $grayTone7;
      background: $grayTone1;
      border: 1px solid $grayTone2;
      box-sizing: border-box;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      padding: 20px 22px 20px 30px;
      transition: .3s ease-in, .3s ease-in-out;
      &:hover{
        border-color: #099FE3;
        border-left-color: $grayTone2;
      }
      @include media(xss){
        padding: 16px;
      }

      svg {
        width: 24px;
        min-width: 24px;
        height: 24px;
      }
    }

    &__name {

    }
  }

  &__right {
    @include media(md) {
      display: none;
    }
  }


}