import clock from '../../public/images/icon/clock_ic.svg'
import JobAction from '../../component/Job/JobAction'
import JobInformation from '../../component/Job/JobInformation'
import Layout from '../../component/Layout'
import styles from '../../styles/Job.module.scss'
import axios from 'axios'
import defaultAvatar from '../../public/images/icon/avatar.svg'
import Link from 'next/link'
import getToken from '../../utils/getToken'
import {useTypedSelector} from '../../hook/useTypedSelector'
import ApplyPopup from '../../component/Job/ApplyPopup'
import SuccessfullyApplyPopup from "../../component/Job/SuccessfullyApplyPopup";
import {useEffect} from "react";
import {store} from "../../store";
import { changeJobApply, changeJobSave, changeJobSubscribe } from '../../store/action-creator/jobs'
import getNoDecodeToken from "../../utils/getNoDecodeToken";

const dayPublishConvert = (publishDay: string): number => {
  return Math.round((Date.now() - new Date(publishDay).valueOf()) / 86400000)
}
//TODO: need to add types interface for all page

// @ts-ignore
const Job = ({job, permissions, token}) => {
  const {applyPopup, successApplyPopup} = useTypedSelector(state => state.app)

  useEffect(() => {
    if (token !== "Empty")    {
      if (job.subscribes[0]){
        store.dispatch(changeJobSave(job.subscribes[0].saveJob))
        store.dispatch(changeJobSubscribe(job.subscribes[0].subscribeJob))
        store.dispatch(changeJobApply(job.subscribes[0].applyJob))
      } else{
        store.dispatch(changeJobSave(false))
        store.dispatch(changeJobSubscribe(false))
        store.dispatch(changeJobApply(false))
      }
    }
  }, [job])


  return (
    <Layout>
      <section className={styles.job}>
        <div className={styles.job__head}>
          <div className={styles.job__head__container}>
            <div className={styles.job__head__inner}>
              <div className={styles.job__head__top}>
                <Link href={'/jobs'}>
                  <a className={styles.job__head__breadcrumbs}>
                    Jobs Marketplace
                  </a>
                </Link>
              </div>
              <div className={styles.job__head__bottom}>
                <div className={styles.job__head__logo}>
                  <img src={job?.company?.avatar ? job?.company.avatar : defaultAvatar.src} alt={job?.title}/>
                </div>
                <div className={styles.job__head__right}>
                  <p className={styles.job__head__headline}>
                    {job?.title}
                  </p>
                  <ul className={styles.job__head__info}>
                    <li className={styles.job__head__info__item}>
                      <Link href={`https://app.urecruits.com/candidate/company/${job.company.tenantId}`}>
                        <a className={styles.job__head__info__link}>
                          {job?.company.name}
                        </a>
                      </Link>
                    </li>
                    {
                      job?.locations[0]?.city &&<li className={`${styles.job__head__info__item} ${styles.location}`}>
                      <span>
                      {
                        `${job.locations[0].city}, ${job.locations[0].state}`
                      }
                      </span>
                      </li>
                    }
                    <li className={`${styles.job__head__info__item} ${styles.gray}`}>
                      <img src={clock.src} alt="" className={styles.job__head__info__icon}/>
                      {dayPublishConvert(job?.createdAt) === 0 ? 'today' : dayPublishConvert(job?.createdAt) + ' days ago'}
                    </li>
                  </ul>
                  <JobAction
                    color="white"
                    permissions={permissions}
                    token={token}
                    job={job}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.job__body}>
          <div className={styles.job__left}>
            <div className={styles.job__left__inner}>
              {
                job?.description && (
                  <>
                    <p className={styles.job__headline}>
                      Job Description
                    </p>
                    <div className={styles.job__content} dangerouslySetInnerHTML={{__html: `${job.description}`}}></div>
                  </>
                )
              }
              {
                job?.skills && (
                  <>
                    <p className={styles.job__headline}>
                      Skills
                    </p>
                    <ul className={styles.job__list}>
                      {
                        job.skills.map((item: any, index: number) => {
                          return (
                            <li className={styles.job__list__item} key={index}>{item} </li>
                          )
                        })
                      }
                    </ul>
                  </>
                )
              }
              {
                job?.aboutCompany && (
                  <>
                    <p className={styles.job__headline}>
                      About Company
                    </p>
                    <div className={styles.job__content}
                         dangerouslySetInnerHTML={{__html: `${job.aboutCompany}`}}></div>
                  </>
                )
              }
            </div>
          </div>
          <div className={styles.job__right}>
            <JobInformation
              data={job}
              permissions={permissions}
              token={token}
              job={job}
            />
          </div>
        </div>
      </section>
      {applyPopup.visible && <ApplyPopup/>}
      {successApplyPopup && <SuccessfullyApplyPopup/>}
    </Layout>
  )
}
export default Job

export async function getServerSideProps(context: { query: { id: string }, res: any, req: any }) {
  const userData = await getToken(context.req, context.res)
  const noDecodeToken = await getNoDecodeToken(context.req, context.res);

  const jobReq = await axios(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/job/public-job/${context.query.id}`, {
    params: {
      currentUserId: userData["https://urecruits.com/userId"],
    }
  })

  if (!jobReq?.data){
    return {
      redirect: {
        destination: '/empty-page',
        permanent: false,
      }
    }
  }

  return {
    props: {
      job: jobReq.data,
      permissions: !!userData?.permissions?.includes('recruiter'),
      token: !!noDecodeToken ? noDecodeToken: 'Empty',
    },
  }
}
