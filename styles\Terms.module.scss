@import "config";
@import "mixins";

.terms {
  min-height: calc(100vh - 683px);
  &__inner {
    @include container;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-bottom: 25px;
  }

  table {
    margin-bottom: 20px;
    display: block;
    overflow-x: auto;
    white-space: pre-wrap;

    tr {
      th {
        font-weight: 900;
      }

      td, th {
        border: 1px solid $grayTone3;
        padding: 10px;
        min-width: 200px;

        span {
          margin-bottom: 5px;
        }
      }
    }
  }

  p {
    margin-bottom: 20px;

    i {
      font-style: italic;
      width: 100%;

      span {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }

  span {
    margin-top: -10px;
    margin-bottom: 20px;
  }
a{
  color: $grayTone5;
  font-weight: 800;
  transition: .3s ease-in,.3s ease-in-out;
  &:hover{
    color: $mainGreen;

  }
}
  em {
    display: flex;
    flex-direction: column;

    span {
      margin-bottom: 10px;
    }
  }

  ul {
    margin-bottom: 20px;

    li {
      padding-left: 20px;
      list-style: disc inside;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}