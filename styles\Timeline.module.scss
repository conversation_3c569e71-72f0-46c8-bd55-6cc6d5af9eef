@import "config";
@import "mixins";

.timeline {
  &__inner {
    @include container();
  }

  &__content {
    display: flex;
    overflow-x: hidden;
    @include media(md) {
      display: none;
    }
  }

  &__mobile {
    display: none;
    @include media(md) {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    &__headline {
      width: 100%;
      margin-bottom: 32px;
    }

    &__item {
      width: calc(50% - 16px);
      box-shadow: 0 0 30px rgb(53, 62, 73, .10);
      border-radius: 16px;
      margin-bottom: 32px;
      padding: 16px;
      @include media(xs){
        width: 100%;
        margin-bottom: 24px;
      }
    }

    &__year {
      font-weight: 800;
      font-size: 32px;
      text-align: center;
      color: $black;
      margin-bottom: 16px;
      @include media(xs){
        font-size: 28px;
      }
    }

    &__title {
      font-size: 20px;
      color: $black;
      font-weight: bold;
      margin-bottom: 8px;
    }

    &__description {
    }
  }

  &__item {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 16.6%;

    &:last-child {
      .timeline__body__line:after {
        display: none;
      }
      .timeline__body__line{
        background: linear-gradient(-90deg, #e5e8eb26 1.56%, #80808038 100%);
      }

    }

    &.progress {
      .timeline__body__line {
        background: $mainGreen;
      }

      .timeline__body__dots {
        background: $mainGreen;
        border: 3px solid $mainGreen;
      }
    }

    &.active,
    &.active,
    &.active,
    &.active {
      .timeline__body__line {
        background: $mainGreen;
      }

      .timeline__body__dots {
        background: $white;
        border: 3px solid $mainGreen;
      }

      &:first-child {
        .timeline__body__line {
          background: linear-gradient(90deg, rgba(9, 156, 115, 0) 1.56%, #099C73 100%);
        }

        .timeline__body__dots {
          background: $white;
          border: 3px solid $mainGreen;
        }
      }

      &:last-child {
        .timeline__body__line {
          background: linear-gradient(-90deg, rgba(9, 156, 115, 0) 1.56%, #099C73 82.95%, rgba(9, 156, 115, 0) 100%);
        }
      }
    }

    &.timeline1{
      .timeline__body__line {
        background: linear-gradient(90deg, rgba(9, 156, 115, 0) 1.56%, #099C73 100%);
      }
    }


  }

  &__head {
    transform: translateX(50%);
    min-height: 150px;

    &__value {
      font-size: 60px;
      font-weight: 800;
      color: $black;
      text-align: center;
    }
  }

  &__body {
    position: relative;

    &__line {
      height: 4px;
      width: 100%;
      background: #80808038;
      position: relative;
      cursor: pointer;
    }

    &__dots {
      top: 0;
      right: 0;
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 3px solid $grayTone3;
      background: $grayTone3;
      transform: translate(12px, -9px);
      z-index: 1;
    }
  }

  &__bottom {
    margin-top: 70px;
    width: 360px;
    transform: translateX(calc(50% - 150px));
    @include media(lg) {
      width: 300px;
    }

    &__title {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 18px;
      color: $black;
      text-align: center;
    }

    &__description {
      text-align: center;
    }
  }
}